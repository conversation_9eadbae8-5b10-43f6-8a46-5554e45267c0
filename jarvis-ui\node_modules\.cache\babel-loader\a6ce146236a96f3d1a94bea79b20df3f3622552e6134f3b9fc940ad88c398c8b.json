{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useRef, useCallback } from 'react';\nconst usePythonAudioBackend = ({\n  enabled = true,\n  serverUrl = 'ws://localhost:8765',\n  // C# backend (default)\n  pythonServerUrl = 'ws://localhost:8766',\n  // Python backend (alternative)\n  usePythonBackend = false,\n  // Set to true to use Python backend\n  reconnectInterval = 3000,\n  maxReconnectAttempts = 10\n}) => {\n  _s();\n  // Audio data state\n  const [audioLevel, setAudioLevel] = useState(0);\n  const [bassLevel, setBassLevel] = useState(0);\n  const [midLevel, setMidLevel] = useState(0);\n  const [trebleLevel, setTrebleLevel] = useState(0);\n  const [beatDetected, setBeatDetected] = useState(false);\n  const [audioSource, setAudioSource] = useState('none');\n  const [isSystemAudio, setIsSystemAudio] = useState(false);\n\n  // Advanced frequency bands\n  const [subBassLevel, setSubBassLevel] = useState(0);\n  const [lowMidrangeLevel, setLowMidrangeLevel] = useState(0);\n  const [upperMidrangeLevel, setUpperMidrangeLevel] = useState(0);\n  const [presenceLevel, setPresenceLevel] = useState(0);\n  const [brillianceLevel, setBrillianceLevel] = useState(0);\n\n  // Advanced beat detection\n  const [subBassBeat, setSubBassBeat] = useState(false);\n  const [bassBeat, setBassBeat] = useState(false);\n  const [lowMidrangeBeat, setLowMidrangeBeat] = useState(false);\n  const [midrangeBeat, setMidrangeBeat] = useState(false);\n  const [upperMidrangeBeat, setUpperMidrangeBeat] = useState(false);\n  const [presenceBeat, setPresenceBeat] = useState(false);\n  const [brillianceBeat, setBrillianceBeat] = useState(false);\n\n  // Connection state\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [reconnectAttempts, setReconnectAttempts] = useState(0);\n\n  // Refs\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const lastDataRef = useRef(null);\n\n  // Beat detection smoothing\n  const beatTimeoutRef = useRef(null);\n  const connect = useCallback(() => {\n    var _wsRef$current;\n    if (!enabled) {\n      console.log('🚫 Python audio backend disabled');\n      return;\n    }\n    if (((_wsRef$current = wsRef.current) === null || _wsRef$current === void 0 ? void 0 : _wsRef$current.readyState) === WebSocket.OPEN) {\n      console.log('🔌 WebSocket already connected');\n      return;\n    }\n    try {\n      const targetUrl = usePythonBackend ? pythonServerUrl : serverUrl;\n      const backendType = usePythonBackend ? 'Python FFT' : 'C# WASAPI';\n      console.log(`🔌 Connecting to ${backendType} audio backend: ${targetUrl}`);\n      const ws = new WebSocket(targetUrl);\n      wsRef.current = ws;\n      ws.onopen = () => {\n        console.log(`✅ Connected to ${backendType} audio backend`);\n        setIsConnected(true);\n        setConnectionError(null);\n        setReconnectAttempts(0);\n      };\n      ws.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n\n          // Update audio data\n          setAudioLevel(data.audioLevel || 0);\n          setBassLevel(data.bassLevel || 0);\n          setMidLevel(data.midLevel || 0);\n          setTrebleLevel(data.trebleLevel || 0);\n          setAudioSource(data.source || 'none');\n          setIsSystemAudio(true); // C# backend is always system audio\n\n          // Update advanced frequency bands\n          setSubBassLevel(data.subBassLevel || 0);\n          setLowMidrangeLevel(data.lowMidrangeLevel || 0);\n          setUpperMidrangeLevel(data.upperMidrangeLevel || 0);\n          setPresenceLevel(data.presenceLevel || 0);\n          setBrillianceLevel(data.brillianceLevel || 0);\n\n          // Update advanced beat detection\n          setSubBassBeat(data.subBassBeat || false);\n          setBassBeat(data.bassBeat || false);\n          setLowMidrangeBeat(data.lowMidrangeBeat || false);\n          setMidrangeBeat(data.midrangeBeat || false);\n          setUpperMidrangeBeat(data.upperMidrangeBeat || false);\n          setPresenceBeat(data.presenceBeat || false);\n          setBrillianceBeat(data.brillianceBeat || false);\n\n          // Handle beat detection with visual feedback\n          if (data.beatDetected) {\n            setBeatDetected(true);\n\n            // Clear previous timeout\n            if (beatTimeoutRef.current) {\n              clearTimeout(beatTimeoutRef.current);\n            }\n\n            // Reset beat detection after 200ms\n            beatTimeoutRef.current = setTimeout(() => {\n              setBeatDetected(false);\n            }, 200);\n          }\n          lastDataRef.current = data;\n        } catch (error) {\n          console.error('❌ Error parsing audio data:', error);\n        }\n      };\n      ws.onclose = event => {\n        console.log('🔌 WebSocket connection closed:', event.code, event.reason);\n        setIsConnected(false);\n        wsRef.current = null;\n\n        // Attempt to reconnect if enabled and not manually closed\n        if (enabled && event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {\n          console.log(`🔄 Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`);\n          setReconnectAttempts(prev => prev + 1);\n          reconnectTimeoutRef.current = setTimeout(() => {\n            connect();\n          }, reconnectInterval);\n        } else if (reconnectAttempts >= maxReconnectAttempts) {\n          setConnectionError('Max reconnection attempts reached');\n          console.error('❌ Max reconnection attempts reached');\n        }\n      };\n      ws.onerror = error => {\n        console.error('❌ WebSocket error:', error);\n        setConnectionError('Connection failed');\n      };\n    } catch (error) {\n      console.error('❌ Failed to create WebSocket connection:', error);\n      setConnectionError(error.message);\n    }\n  }, [enabled, serverUrl, reconnectInterval, maxReconnectAttempts, reconnectAttempts]);\n  const disconnect = useCallback(() => {\n    console.log('🔌 Disconnecting from Python audio backend');\n\n    // Clear reconnection timeout\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n\n    // Clear beat timeout\n    if (beatTimeoutRef.current) {\n      clearTimeout(beatTimeoutRef.current);\n      beatTimeoutRef.current = null;\n    }\n\n    // Close WebSocket connection\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect');\n      wsRef.current = null;\n    }\n\n    // Reset state\n    setIsConnected(false);\n    setConnectionError(null);\n    setReconnectAttempts(0);\n    setAudioLevel(0);\n    setBassLevel(0);\n    setMidLevel(0);\n    setTrebleLevel(0);\n    setBeatDetected(false);\n    setAudioSource('none');\n    setIsSystemAudio(false);\n\n    // Reset advanced frequency bands\n    setSubBassLevel(0);\n    setLowMidrangeLevel(0);\n    setUpperMidrangeLevel(0);\n    setPresenceLevel(0);\n    setBrillianceLevel(0);\n\n    // Reset advanced beat detection\n    setSubBassBeat(false);\n    setBassBeat(false);\n    setLowMidrangeBeat(false);\n    setMidrangeBeat(false);\n    setUpperMidrangeBeat(false);\n    setPresenceBeat(false);\n    setBrillianceBeat(false);\n  }, []);\n  const reconnect = useCallback(() => {\n    console.log('🔄 Manual reconnection requested');\n    disconnect();\n    setTimeout(() => {\n      setReconnectAttempts(0);\n      connect();\n    }, 1000);\n  }, [disconnect, connect]);\n\n  // Auto-connect when enabled\n  useEffect(() => {\n    if (enabled) {\n      connect();\n    } else {\n      disconnect();\n    }\n    return () => {\n      disconnect();\n    };\n  }, [enabled, connect, disconnect]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  // Connection status helper\n  const getConnectionStatus = useCallback(() => {\n    if (isConnected) {\n      return {\n        status: 'connected',\n        message: `Connected to Python backend (${isSystemAudio ? 'System Audio' : 'Microphone'})`\n      };\n    } else if (connectionError) {\n      return {\n        status: 'error',\n        message: `Connection error: ${connectionError}`\n      };\n    } else if (reconnectAttempts > 0) {\n      return {\n        status: 'reconnecting',\n        message: `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`\n      };\n    } else {\n      return {\n        status: 'disconnected',\n        message: 'Not connected to Python backend'\n      };\n    }\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, isSystemAudio]);\n\n  // Debug info\n  const getDebugInfo = useCallback(() => {\n    var _wsRef$current2;\n    return {\n      isConnected,\n      connectionError,\n      reconnectAttempts,\n      maxReconnectAttempts,\n      serverUrl,\n      lastData: lastDataRef.current,\n      wsReadyState: (_wsRef$current2 = wsRef.current) === null || _wsRef$current2 === void 0 ? void 0 : _wsRef$current2.readyState\n    };\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, serverUrl]);\n\n  // Computed values - Maximum sensitivity\n  const isAudioActive = audioLevel > 0.5; // Consider audio active if level > 0.5%\n\n  return {\n    // Basic audio data\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    beatDetected,\n    isAudioActive,\n    audioSource,\n    isSystemAudio,\n    // Advanced frequency bands (20Hz-20kHz spectrum analysis)\n    subBassLevel,\n    // 20-60 Hz\n    lowMidrangeLevel,\n    // 250-500 Hz\n    upperMidrangeLevel,\n    // 2000-4000 Hz\n    presenceLevel,\n    // 4000-6000 Hz\n    brillianceLevel,\n    // 6000-20000 Hz\n\n    // Advanced beat detection per frequency band\n    subBassBeat,\n    bassBeat,\n    lowMidrangeBeat,\n    midrangeBeat,\n    upperMidrangeBeat,\n    presenceBeat,\n    brillianceBeat,\n    // Connection state\n    isConnected,\n    connectionError,\n    reconnectAttempts,\n    // Connection controls\n    connect,\n    disconnect,\n    reconnect,\n    // Helpers\n    getConnectionStatus,\n    getDebugInfo\n  };\n};\n_s(usePythonAudioBackend, \"nVDChelb9gMlZQZ3cI5nlHGkcwo=\");\nexport default usePythonAudioBackend;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useCallback", "usePythonAudioBackend", "enabled", "serverUrl", "pythonServerUrl", "usePythonBackend", "reconnectInterval", "maxReconnectAttempts", "_s", "audioLevel", "setAudioLevel", "bassLevel", "setBassLevel", "midLevel", "setMidLevel", "trebleLevel", "setTrebleLevel", "beatDetected", "setBeatDetected", "audioSource", "setAudioSource", "isSystemAudio", "setIsSystemAudio", "subBassLevel", "setSubBassLevel", "lowMidrangeLevel", "setLowMidrangeLevel", "upperMidrangeLevel", "setUpperMidrangeLevel", "presenceLevel", "setPresenceLevel", "brillianceLevel", "setBrillianceLevel", "subBassBeat", "setSubBassBeat", "bassBeat", "setBassBeat", "lowMidrangeBeat", "setLowMidrangeBeat", "midrangeBeat", "setMidrangeBeat", "upperMidrangeBeat", "setUpperMidrangeBeat", "presenceBeat", "setPresenceBeat", "brillianceBeat", "setBrillianceBeat", "isConnected", "setIsConnected", "connectionError", "setConnectionError", "reconnectAttempts", "setReconnectAttempts", "wsRef", "reconnectTimeoutRef", "lastDataRef", "beatTimeoutRef", "connect", "_wsRef$current", "console", "log", "current", "readyState", "WebSocket", "OPEN", "targetUrl", "backendType", "ws", "onopen", "onmessage", "event", "data", "JSON", "parse", "source", "clearTimeout", "setTimeout", "error", "onclose", "code", "reason", "prev", "onerror", "message", "disconnect", "close", "reconnect", "getConnectionStatus", "status", "getDebugInfo", "_wsRef$current2", "lastData", "wsReadyState", "isAudioActive"], "sources": ["C:/Users/<USER>/My Code Work/UI Designolddd/jarvis-ui/src/hooks/usePythonAudioBackend.js"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react';\n\nconst usePythonAudioBackend = ({\n  enabled = true,\n  serverUrl = 'ws://localhost:8765', // C# backend (default)\n  pythonServerUrl = 'ws://localhost:8766', // Python backend (alternative)\n  usePythonBackend = false, // Set to true to use Python backend\n  reconnectInterval = 3000,\n  maxReconnectAttempts = 10\n}) => {\n  // Audio data state\n  const [audioLevel, setAudioLevel] = useState(0);\n  const [bassLevel, setBassLevel] = useState(0);\n  const [midLevel, setMidLevel] = useState(0);\n  const [trebleLevel, setTrebleLevel] = useState(0);\n  const [beatDetected, setBeatDetected] = useState(false);\n  const [audioSource, setAudioSource] = useState('none');\n  const [isSystemAudio, setIsSystemAudio] = useState(false);\n\n  // Advanced frequency bands\n  const [subBassLevel, setSubBassLevel] = useState(0);\n  const [lowMidrangeLevel, setLowMidrangeLevel] = useState(0);\n  const [upperMidrangeLevel, setUpperMidrangeLevel] = useState(0);\n  const [presenceLevel, setPresenceLevel] = useState(0);\n  const [brillianceLevel, setBrillianceLevel] = useState(0);\n\n  // Advanced beat detection\n  const [subBassBeat, setSubBassBeat] = useState(false);\n  const [bassBeat, setBassBeat] = useState(false);\n  const [lowMidrangeBeat, setLowMidrangeBeat] = useState(false);\n  const [midrangeBeat, setMidrangeBeat] = useState(false);\n  const [upperMidrangeBeat, setUpperMidrangeBeat] = useState(false);\n  const [presenceBeat, setPresenceBeat] = useState(false);\n  const [brillianceBeat, setBrillianceBeat] = useState(false);\n  \n  // Connection state\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [reconnectAttempts, setReconnectAttempts] = useState(0);\n  \n  // Refs\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const lastDataRef = useRef(null);\n  \n  // Beat detection smoothing\n  const beatTimeoutRef = useRef(null);\n  \n  const connect = useCallback(() => {\n    if (!enabled) {\n      console.log('🚫 Python audio backend disabled');\n      return;\n    }\n    \n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      console.log('🔌 WebSocket already connected');\n      return;\n    }\n    \n    try {\n      const targetUrl = usePythonBackend ? pythonServerUrl : serverUrl;\n      const backendType = usePythonBackend ? 'Python FFT' : 'C# WASAPI';\n\n      console.log(`🔌 Connecting to ${backendType} audio backend: ${targetUrl}`);\n\n      const ws = new WebSocket(targetUrl);\n      wsRef.current = ws;\n      \n      ws.onopen = () => {\n        console.log(`✅ Connected to ${backendType} audio backend`);\n        setIsConnected(true);\n        setConnectionError(null);\n        setReconnectAttempts(0);\n      };\n      \n      ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          \n          // Update audio data\n          setAudioLevel(data.audioLevel || 0);\n          setBassLevel(data.bassLevel || 0);\n          setMidLevel(data.midLevel || 0);\n          setTrebleLevel(data.trebleLevel || 0);\n          setAudioSource(data.source || 'none');\n          setIsSystemAudio(true); // C# backend is always system audio\n\n          // Update advanced frequency bands\n          setSubBassLevel(data.subBassLevel || 0);\n          setLowMidrangeLevel(data.lowMidrangeLevel || 0);\n          setUpperMidrangeLevel(data.upperMidrangeLevel || 0);\n          setPresenceLevel(data.presenceLevel || 0);\n          setBrillianceLevel(data.brillianceLevel || 0);\n\n          // Update advanced beat detection\n          setSubBassBeat(data.subBassBeat || false);\n          setBassBeat(data.bassBeat || false);\n          setLowMidrangeBeat(data.lowMidrangeBeat || false);\n          setMidrangeBeat(data.midrangeBeat || false);\n          setUpperMidrangeBeat(data.upperMidrangeBeat || false);\n          setPresenceBeat(data.presenceBeat || false);\n          setBrillianceBeat(data.brillianceBeat || false);\n          \n          // Handle beat detection with visual feedback\n          if (data.beatDetected) {\n            setBeatDetected(true);\n            \n            // Clear previous timeout\n            if (beatTimeoutRef.current) {\n              clearTimeout(beatTimeoutRef.current);\n            }\n            \n            // Reset beat detection after 200ms\n            beatTimeoutRef.current = setTimeout(() => {\n              setBeatDetected(false);\n            }, 200);\n          }\n          \n          lastDataRef.current = data;\n          \n        } catch (error) {\n          console.error('❌ Error parsing audio data:', error);\n        }\n      };\n      \n      ws.onclose = (event) => {\n        console.log('🔌 WebSocket connection closed:', event.code, event.reason);\n        setIsConnected(false);\n        wsRef.current = null;\n        \n        // Attempt to reconnect if enabled and not manually closed\n        if (enabled && event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {\n          console.log(`🔄 Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`);\n          setReconnectAttempts(prev => prev + 1);\n          \n          reconnectTimeoutRef.current = setTimeout(() => {\n            connect();\n          }, reconnectInterval);\n        } else if (reconnectAttempts >= maxReconnectAttempts) {\n          setConnectionError('Max reconnection attempts reached');\n          console.error('❌ Max reconnection attempts reached');\n        }\n      };\n      \n      ws.onerror = (error) => {\n        console.error('❌ WebSocket error:', error);\n        setConnectionError('Connection failed');\n      };\n      \n    } catch (error) {\n      console.error('❌ Failed to create WebSocket connection:', error);\n      setConnectionError(error.message);\n    }\n  }, [enabled, serverUrl, reconnectInterval, maxReconnectAttempts, reconnectAttempts]);\n  \n  const disconnect = useCallback(() => {\n    console.log('🔌 Disconnecting from Python audio backend');\n    \n    // Clear reconnection timeout\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n    \n    // Clear beat timeout\n    if (beatTimeoutRef.current) {\n      clearTimeout(beatTimeoutRef.current);\n      beatTimeoutRef.current = null;\n    }\n    \n    // Close WebSocket connection\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect');\n      wsRef.current = null;\n    }\n    \n    // Reset state\n    setIsConnected(false);\n    setConnectionError(null);\n    setReconnectAttempts(0);\n    setAudioLevel(0);\n    setBassLevel(0);\n    setMidLevel(0);\n    setTrebleLevel(0);\n    setBeatDetected(false);\n    setAudioSource('none');\n    setIsSystemAudio(false);\n\n    // Reset advanced frequency bands\n    setSubBassLevel(0);\n    setLowMidrangeLevel(0);\n    setUpperMidrangeLevel(0);\n    setPresenceLevel(0);\n    setBrillianceLevel(0);\n\n    // Reset advanced beat detection\n    setSubBassBeat(false);\n    setBassBeat(false);\n    setLowMidrangeBeat(false);\n    setMidrangeBeat(false);\n    setUpperMidrangeBeat(false);\n    setPresenceBeat(false);\n    setBrillianceBeat(false);\n  }, []);\n  \n  const reconnect = useCallback(() => {\n    console.log('🔄 Manual reconnection requested');\n    disconnect();\n    setTimeout(() => {\n      setReconnectAttempts(0);\n      connect();\n    }, 1000);\n  }, [disconnect, connect]);\n  \n  // Auto-connect when enabled\n  useEffect(() => {\n    if (enabled) {\n      connect();\n    } else {\n      disconnect();\n    }\n    \n    return () => {\n      disconnect();\n    };\n  }, [enabled, connect, disconnect]);\n  \n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n  \n  // Connection status helper\n  const getConnectionStatus = useCallback(() => {\n    if (isConnected) {\n      return {\n        status: 'connected',\n        message: `Connected to Python backend (${isSystemAudio ? 'System Audio' : 'Microphone'})`\n      };\n    } else if (connectionError) {\n      return {\n        status: 'error',\n        message: `Connection error: ${connectionError}`\n      };\n    } else if (reconnectAttempts > 0) {\n      return {\n        status: 'reconnecting',\n        message: `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`\n      };\n    } else {\n      return {\n        status: 'disconnected',\n        message: 'Not connected to Python backend'\n      };\n    }\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, isSystemAudio]);\n  \n  // Debug info\n  const getDebugInfo = useCallback(() => {\n    return {\n      isConnected,\n      connectionError,\n      reconnectAttempts,\n      maxReconnectAttempts,\n      serverUrl,\n      lastData: lastDataRef.current,\n      wsReadyState: wsRef.current?.readyState\n    };\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, serverUrl]);\n  \n  // Computed values - Maximum sensitivity\n  const isAudioActive = audioLevel > 0.5; // Consider audio active if level > 0.5%\n\n  return {\n    // Basic audio data\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    beatDetected,\n    isAudioActive,\n    audioSource,\n    isSystemAudio,\n\n    // Advanced frequency bands (20Hz-20kHz spectrum analysis)\n    subBassLevel,        // 20-60 Hz\n    lowMidrangeLevel,    // 250-500 Hz\n    upperMidrangeLevel,  // 2000-4000 Hz\n    presenceLevel,       // 4000-6000 Hz\n    brillianceLevel,     // 6000-20000 Hz\n\n    // Advanced beat detection per frequency band\n    subBassBeat,\n    bassBeat,\n    lowMidrangeBeat,\n    midrangeBeat,\n    upperMidrangeBeat,\n    presenceBeat,\n    brillianceBeat,\n\n    // Connection state\n    isConnected,\n    connectionError,\n    reconnectAttempts,\n\n    // Connection controls\n    connect,\n    disconnect,\n    reconnect,\n\n    // Helpers\n    getConnectionStatus,\n    getDebugInfo\n  };\n};\n\nexport default usePythonAudioBackend;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAEhE,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,OAAO,GAAG,IAAI;EACdC,SAAS,GAAG,qBAAqB;EAAE;EACnCC,eAAe,GAAG,qBAAqB;EAAE;EACzCC,gBAAgB,GAAG,KAAK;EAAE;EAC1BC,iBAAiB,GAAG,IAAI;EACxBC,oBAAoB,GAAG;AACzB,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;;EAE7D;EACA,MAAMwD,KAAK,GAAGtD,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMuD,mBAAmB,GAAGvD,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMwD,WAAW,GAAGxD,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMyD,cAAc,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAM0D,OAAO,GAAGzD,WAAW,CAAC,MAAM;IAAA,IAAA0D,cAAA;IAChC,IAAI,CAACxD,OAAO,EAAE;MACZyD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C;IACF;IAEA,IAAI,EAAAF,cAAA,GAAAL,KAAK,CAACQ,OAAO,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;MAChDL,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C;IACF;IAEA,IAAI;MACF,MAAMK,SAAS,GAAG5D,gBAAgB,GAAGD,eAAe,GAAGD,SAAS;MAChE,MAAM+D,WAAW,GAAG7D,gBAAgB,GAAG,YAAY,GAAG,WAAW;MAEjEsD,OAAO,CAACC,GAAG,CAAC,oBAAoBM,WAAW,mBAAmBD,SAAS,EAAE,CAAC;MAE1E,MAAME,EAAE,GAAG,IAAIJ,SAAS,CAACE,SAAS,CAAC;MACnCZ,KAAK,CAACQ,OAAO,GAAGM,EAAE;MAElBA,EAAE,CAACC,MAAM,GAAG,MAAM;QAChBT,OAAO,CAACC,GAAG,CAAC,kBAAkBM,WAAW,gBAAgB,CAAC;QAC1DlB,cAAc,CAAC,IAAI,CAAC;QACpBE,kBAAkB,CAAC,IAAI,CAAC;QACxBE,oBAAoB,CAAC,CAAC,CAAC;MACzB,CAAC;MAEDe,EAAE,CAACE,SAAS,GAAIC,KAAK,IAAK;QACxB,IAAI;UACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;;UAEnC;UACA7D,aAAa,CAAC6D,IAAI,CAAC9D,UAAU,IAAI,CAAC,CAAC;UACnCG,YAAY,CAAC2D,IAAI,CAAC5D,SAAS,IAAI,CAAC,CAAC;UACjCG,WAAW,CAACyD,IAAI,CAAC1D,QAAQ,IAAI,CAAC,CAAC;UAC/BG,cAAc,CAACuD,IAAI,CAACxD,WAAW,IAAI,CAAC,CAAC;UACrCK,cAAc,CAACmD,IAAI,CAACG,MAAM,IAAI,MAAM,CAAC;UACrCpD,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAExB;UACAE,eAAe,CAAC+C,IAAI,CAAChD,YAAY,IAAI,CAAC,CAAC;UACvCG,mBAAmB,CAAC6C,IAAI,CAAC9C,gBAAgB,IAAI,CAAC,CAAC;UAC/CG,qBAAqB,CAAC2C,IAAI,CAAC5C,kBAAkB,IAAI,CAAC,CAAC;UACnDG,gBAAgB,CAACyC,IAAI,CAAC1C,aAAa,IAAI,CAAC,CAAC;UACzCG,kBAAkB,CAACuC,IAAI,CAACxC,eAAe,IAAI,CAAC,CAAC;;UAE7C;UACAG,cAAc,CAACqC,IAAI,CAACtC,WAAW,IAAI,KAAK,CAAC;UACzCG,WAAW,CAACmC,IAAI,CAACpC,QAAQ,IAAI,KAAK,CAAC;UACnCG,kBAAkB,CAACiC,IAAI,CAAClC,eAAe,IAAI,KAAK,CAAC;UACjDG,eAAe,CAAC+B,IAAI,CAAChC,YAAY,IAAI,KAAK,CAAC;UAC3CG,oBAAoB,CAAC6B,IAAI,CAAC9B,iBAAiB,IAAI,KAAK,CAAC;UACrDG,eAAe,CAAC2B,IAAI,CAAC5B,YAAY,IAAI,KAAK,CAAC;UAC3CG,iBAAiB,CAACyB,IAAI,CAAC1B,cAAc,IAAI,KAAK,CAAC;;UAE/C;UACA,IAAI0B,IAAI,CAACtD,YAAY,EAAE;YACrBC,eAAe,CAAC,IAAI,CAAC;;YAErB;YACA,IAAIsC,cAAc,CAACK,OAAO,EAAE;cAC1Bc,YAAY,CAACnB,cAAc,CAACK,OAAO,CAAC;YACtC;;YAEA;YACAL,cAAc,CAACK,OAAO,GAAGe,UAAU,CAAC,MAAM;cACxC1D,eAAe,CAAC,KAAK,CAAC;YACxB,CAAC,EAAE,GAAG,CAAC;UACT;UAEAqC,WAAW,CAACM,OAAO,GAAGU,IAAI;QAE5B,CAAC,CAAC,OAAOM,KAAK,EAAE;UACdlB,OAAO,CAACkB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF,CAAC;MAEDV,EAAE,CAACW,OAAO,GAAIR,KAAK,IAAK;QACtBX,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEU,KAAK,CAACS,IAAI,EAAET,KAAK,CAACU,MAAM,CAAC;QACxEhC,cAAc,CAAC,KAAK,CAAC;QACrBK,KAAK,CAACQ,OAAO,GAAG,IAAI;;QAEpB;QACA,IAAI3D,OAAO,IAAIoE,KAAK,CAACS,IAAI,KAAK,IAAI,IAAI5B,iBAAiB,GAAG5C,oBAAoB,EAAE;UAC9EoD,OAAO,CAACC,GAAG,CAAC,+BAA+BT,iBAAiB,GAAG,CAAC,IAAI5C,oBAAoB,MAAM,CAAC;UAC/F6C,oBAAoB,CAAC6B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;UAEtC3B,mBAAmB,CAACO,OAAO,GAAGe,UAAU,CAAC,MAAM;YAC7CnB,OAAO,CAAC,CAAC;UACX,CAAC,EAAEnD,iBAAiB,CAAC;QACvB,CAAC,MAAM,IAAI6C,iBAAiB,IAAI5C,oBAAoB,EAAE;UACpD2C,kBAAkB,CAAC,mCAAmC,CAAC;UACvDS,OAAO,CAACkB,KAAK,CAAC,qCAAqC,CAAC;QACtD;MACF,CAAC;MAEDV,EAAE,CAACe,OAAO,GAAIL,KAAK,IAAK;QACtBlB,OAAO,CAACkB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C3B,kBAAkB,CAAC,mBAAmB,CAAC;MACzC,CAAC;IAEH,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE3B,kBAAkB,CAAC2B,KAAK,CAACM,OAAO,CAAC;IACnC;EACF,CAAC,EAAE,CAACjF,OAAO,EAAEC,SAAS,EAAEG,iBAAiB,EAAEC,oBAAoB,EAAE4C,iBAAiB,CAAC,CAAC;EAEpF,MAAMiC,UAAU,GAAGpF,WAAW,CAAC,MAAM;IACnC2D,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;IAEzD;IACA,IAAIN,mBAAmB,CAACO,OAAO,EAAE;MAC/Bc,YAAY,CAACrB,mBAAmB,CAACO,OAAO,CAAC;MACzCP,mBAAmB,CAACO,OAAO,GAAG,IAAI;IACpC;;IAEA;IACA,IAAIL,cAAc,CAACK,OAAO,EAAE;MAC1Bc,YAAY,CAACnB,cAAc,CAACK,OAAO,CAAC;MACpCL,cAAc,CAACK,OAAO,GAAG,IAAI;IAC/B;;IAEA;IACA,IAAIR,KAAK,CAACQ,OAAO,EAAE;MACjBR,KAAK,CAACQ,OAAO,CAACwB,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MAC9ChC,KAAK,CAACQ,OAAO,GAAG,IAAI;IACtB;;IAEA;IACAb,cAAc,CAAC,KAAK,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,CAAC,CAAC;IACvB1C,aAAa,CAAC,CAAC,CAAC;IAChBE,YAAY,CAAC,CAAC,CAAC;IACfE,WAAW,CAAC,CAAC,CAAC;IACdE,cAAc,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtBE,cAAc,CAAC,MAAM,CAAC;IACtBE,gBAAgB,CAAC,KAAK,CAAC;;IAEvB;IACAE,eAAe,CAAC,CAAC,CAAC;IAClBE,mBAAmB,CAAC,CAAC,CAAC;IACtBE,qBAAqB,CAAC,CAAC,CAAC;IACxBE,gBAAgB,CAAC,CAAC,CAAC;IACnBE,kBAAkB,CAAC,CAAC,CAAC;;IAErB;IACAE,cAAc,CAAC,KAAK,CAAC;IACrBE,WAAW,CAAC,KAAK,CAAC;IAClBE,kBAAkB,CAAC,KAAK,CAAC;IACzBE,eAAe,CAAC,KAAK,CAAC;IACtBE,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,eAAe,CAAC,KAAK,CAAC;IACtBE,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwC,SAAS,GAAGtF,WAAW,CAAC,MAAM;IAClC2D,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CwB,UAAU,CAAC,CAAC;IACZR,UAAU,CAAC,MAAM;MACfxB,oBAAoB,CAAC,CAAC,CAAC;MACvBK,OAAO,CAAC,CAAC;IACX,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAAC2B,UAAU,EAAE3B,OAAO,CAAC,CAAC;;EAEzB;EACA3D,SAAS,CAAC,MAAM;IACd,IAAII,OAAO,EAAE;MACXuD,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACL2B,UAAU,CAAC,CAAC;IACd;IAEA,OAAO,MAAM;MACXA,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAAClF,OAAO,EAAEuD,OAAO,EAAE2B,UAAU,CAAC,CAAC;;EAElC;EACAtF,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXsF,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMG,mBAAmB,GAAGvF,WAAW,CAAC,MAAM;IAC5C,IAAI+C,WAAW,EAAE;MACf,OAAO;QACLyC,MAAM,EAAE,WAAW;QACnBL,OAAO,EAAE,gCAAgC9D,aAAa,GAAG,cAAc,GAAG,YAAY;MACxF,CAAC;IACH,CAAC,MAAM,IAAI4B,eAAe,EAAE;MAC1B,OAAO;QACLuC,MAAM,EAAE,OAAO;QACfL,OAAO,EAAE,qBAAqBlC,eAAe;MAC/C,CAAC;IACH,CAAC,MAAM,IAAIE,iBAAiB,GAAG,CAAC,EAAE;MAChC,OAAO;QACLqC,MAAM,EAAE,cAAc;QACtBL,OAAO,EAAE,oBAAoBhC,iBAAiB,IAAI5C,oBAAoB;MACxE,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLiF,MAAM,EAAE,cAAc;QACtBL,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC,EAAE,CAACpC,WAAW,EAAEE,eAAe,EAAEE,iBAAiB,EAAE5C,oBAAoB,EAAEc,aAAa,CAAC,CAAC;;EAE1F;EACA,MAAMoE,YAAY,GAAGzF,WAAW,CAAC,MAAM;IAAA,IAAA0F,eAAA;IACrC,OAAO;MACL3C,WAAW;MACXE,eAAe;MACfE,iBAAiB;MACjB5C,oBAAoB;MACpBJ,SAAS;MACTwF,QAAQ,EAAEpC,WAAW,CAACM,OAAO;MAC7B+B,YAAY,GAAAF,eAAA,GAAErC,KAAK,CAACQ,OAAO,cAAA6B,eAAA,uBAAbA,eAAA,CAAe5B;IAC/B,CAAC;EACH,CAAC,EAAE,CAACf,WAAW,EAAEE,eAAe,EAAEE,iBAAiB,EAAE5C,oBAAoB,EAAEJ,SAAS,CAAC,CAAC;;EAEtF;EACA,MAAM0F,aAAa,GAAGpF,UAAU,GAAG,GAAG,CAAC,CAAC;;EAExC,OAAO;IACL;IACAA,UAAU;IACVE,SAAS;IACTE,QAAQ;IACRE,WAAW;IACXE,YAAY;IACZ4E,aAAa;IACb1E,WAAW;IACXE,aAAa;IAEb;IACAE,YAAY;IAAS;IACrBE,gBAAgB;IAAK;IACrBE,kBAAkB;IAAG;IACrBE,aAAa;IAAQ;IACrBE,eAAe;IAAM;;IAErB;IACAE,WAAW;IACXE,QAAQ;IACRE,eAAe;IACfE,YAAY;IACZE,iBAAiB;IACjBE,YAAY;IACZE,cAAc;IAEd;IACAE,WAAW;IACXE,eAAe;IACfE,iBAAiB;IAEjB;IACAM,OAAO;IACP2B,UAAU;IACVE,SAAS;IAET;IACAC,mBAAmB;IACnBE;EACF,CAAC;AACH,CAAC;AAACjF,EAAA,CA1TIP,qBAAqB;AA4T3B,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}