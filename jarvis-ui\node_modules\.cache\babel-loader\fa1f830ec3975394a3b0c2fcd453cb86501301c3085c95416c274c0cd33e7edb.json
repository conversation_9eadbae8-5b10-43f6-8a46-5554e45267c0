{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\UI Designolddd\\\\jarvis-ui\\\\src\\\\components\\\\Jarvis.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Jarvis = ({\n  onExitToHome,\n  onEnterChat\n}) => {\n  _s();\n  const [state, setState] = useState('startup');\n  const [micActive, setMicActive] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n\n  // Handle mic toggle with state switching\n  const handleMicToggle = checked => {\n    setMicActive(checked);\n    if (checked) {\n      // Turn on mic -> switch to listening mode\n      setState('listening');\n    } else {\n      // Turn off mic -> return to rest mode (unless processing/speaking)\n      if (state === 'listening') {\n        setState('rest');\n      }\n      // If it's thinking/speaking/startup, don't change state\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      y: 20,\n      opacity: 0\n    },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  // State content configuration\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      loop: true\n    },\n    rest: {\n      media: 'rest.mp4',\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      loop: true\n    },\n    listening: {\n      media: 'listening.gif',\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      loop: true\n    },\n    thinking: {\n      media: 'thinking.gif',\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      loop: true\n    },\n    speaking: {\n      media: 'speaking.gif',\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      loop: true\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-20\",\n      children: /*#__PURE__*/_jsxDEV(SystemAudioStatus, {\n        isSystemAudio: isSystemAudio,\n        audioSource: audioSource,\n        audioLevel: audioLevel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",\n      children: /*#__PURE__*/_jsxDEV(ExpandableSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center justify-center space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative w-96 h-96 rounded-full overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.95\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 1.05\n            },\n            transition: {\n              duration: 0.6,\n              ease: [0.4, 0, 0.2, 1],\n              opacity: {\n                duration: 0.4\n              }\n            },\n            className: \"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\",\n            children: stateConfig[state].media.endsWith('.mp4') ? /*#__PURE__*/_jsxDEV(\"video\", {\n              src: `/assets/${stateConfig[state].media}`,\n              autoPlay: true,\n              muted: true,\n              loop: true,\n              playsInline: true,\n              className: \"w-full h-full object-cover rounded-full\",\n              style: {\n                clipPath: 'circle(50% at 50% 50%)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `/assets/${stateConfig[state].media}`,\n              alt: state,\n              className: `w-full h-full object-cover rounded-full ${state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''}`,\n              style: {\n                clipPath: 'circle(50% at 50% 50%)',\n                imageRendering: 'auto',\n                filter: 'contrast(1.1) brightness(1.05)',\n                transition: 'all 0.3s ease-in-out'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this)\n          }, state, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute inset-0 rounded-full border-4 pointer-events-none\",\n          style: {\n            borderColor: stateConfig[state].color\n          },\n          animate: {\n            opacity: [0.3, 0.8, 0.3],\n            scale: [1, 1.1, 1],\n            boxShadow: `0 0 20px ${stateConfig[state].color}60`\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center space-y-4\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          className: \"text-4xl font-bold tracking-tighter\",\n          style: {\n            color: stateConfig[state].color\n          },\n          animate: {\n            scale: 1 + audioLevel * 0.1 + (beatDetected ? 0.05 : 0),\n            textShadow: `0 0 ${20 + audioLevel * 30}px currentColor, 0 0 ${40 + audioLevel * 60}px currentColor`\n          },\n          variants: itemVariants,\n          transition: {\n            scale: {\n              type: \"spring\",\n              damping: 25,\n              stiffness: 400\n            },\n            textShadow: {\n              duration: 0.1\n            }\n          },\n          children: stateConfig[state].title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          className: \"text-xl text-cyan-200 font-light\",\n          variants: itemVariants,\n          animate: {\n            opacity: 0.8 + midLevel * 0.2,\n            scale: 1 + trebleLevel * 0.05\n          },\n          transition: {\n            opacity: {\n              duration: 0.2\n            },\n            scale: {\n              duration: 0.3\n            }\n          },\n          children: stateConfig[state].description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"mt-12\",\n        variants: itemVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: micActive,\n            onChange: e => handleMicToggle(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"checkmark\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"icon No\",\n              viewBox: \"0 0 24 24\",\n              fill: \"#dc6b6b\",\n              style: {\n                color: '#dc6b6b'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"6\",\n                y1: \"4\",\n                x2: \"18\",\n                y2: \"20\",\n                stroke: \"#dc6b6b\",\n                strokeWidth: \"2.5\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"name No\",\n              children: \"Mic Off\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"icon Yes\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"8\",\n                r: \"1\",\n                opacity: \"0.6\",\n                children: [/*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"r\",\n                  values: \"1;2;1\",\n                  dur: \"1s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"opacity\",\n                  values: \"0.6;0.2;0.6\",\n                  dur: \"1s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"8\",\n                r: \"3\",\n                opacity: \"0.3\",\n                children: [/*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"r\",\n                  values: \"3;4;3\",\n                  dur: \"1.5s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"opacity\",\n                  values: \"0.3;0.1;0.3\",\n                  dur: \"1.5s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"name Yes\",\n              children: \"Listening\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"flex justify-center\",\n        variants: itemVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `form-control ${isInputFocused ? 'focused' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"input\",\n            placeholder: \"Type something intelligent (Press Enter to chat)\",\n            value: inputText,\n            onChange: e => setInputText(e.target.value),\n            onFocus: () => setIsInputFocused(true),\n            onBlur: () => setIsInputFocused(false),\n            onKeyDown: e => {\n              if (e.key === 'Enter' && inputText.trim()) {\n                // Handle text input submission - open chat screen\n                const message = inputText.trim();\n                setInputText('');\n                onEnterChat(message);\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-6 left-6\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"menu\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [Object.keys(stateConfig).map(s => /*#__PURE__*/_jsxDEV(motion.button, {\n          className: `link ${state === s ? 'active' : ''}`,\n          style: {\n            backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',\n            border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'\n          },\n          whileHover: {\n            scale: 1.05,\n            boxShadow: `0 0 12px ${stateConfig[s].color}50`\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setState(s),\n          variants: itemVariants,\n          title: s.toUpperCase(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"link-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",\n              style: {\n                backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',\n                color: 'white',\n                fontSize: '10px'\n              },\n              children: s.charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"link-title\",\n            children: s.charAt(0).toUpperCase() + s.slice(1)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)]\n        }, s, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)), onExitToHome && /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"link\",\n          style: {\n            backgroundColor: 'transparent',\n            border: '1px solid rgba(255, 255, 255, 0.3)'\n          },\n          whileHover: {\n            scale: 1.05,\n            boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: onExitToHome,\n          variants: itemVariants,\n          title: \"HOME\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"link-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",\n              style: {\n                backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                color: 'black',\n                fontSize: '10px'\n              },\n              children: \"H\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"link-title\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AudioBackendTester, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(Jarvis, \"PC3+F3VRwjnBqQJX5TpCb2THicM=\");\n_c = Jarvis;\nexport default Jarvis;\nvar _c;\n$RefreshReg$(_c, \"Jarvis\");", "map": {"version": 3, "names": ["useState", "motion", "AnimatePresence", "ExpandableSidebar", "jsxDEV", "_jsxDEV", "<PERSON>", "onExitToHome", "onEnterChat", "_s", "state", "setState", "micActive", "setMicActive", "inputText", "setInputText", "isInputFocused", "setIsInputFocused", "handleMicToggle", "checked", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "stateConfig", "startup", "media", "title", "description", "color", "loop", "rest", "listening", "thinking", "speaking", "className", "children", "SystemAudioStatus", "isSystemAudio", "audioSource", "audioLevel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "mode", "initial", "scale", "animate", "exit", "ease", "endsWith", "src", "autoPlay", "muted", "playsInline", "style", "clipPath", "alt", "imageRendering", "filter", "borderColor", "boxShadow", "repeat", "Infinity", "variants", "h1", "beatDetected", "textShadow", "type", "damping", "stiffness", "p", "midLevel", "trebleLevel", "onChange", "e", "target", "viewBox", "fill", "d", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "strokeLinecap", "cx", "cy", "r", "attributeName", "values", "dur", "repeatCount", "placeholder", "value", "onFocus", "onBlur", "onKeyDown", "key", "trim", "message", "Object", "keys", "map", "s", "button", "backgroundColor", "border", "whileHover", "whileTap", "onClick", "toUpperCase", "fontSize", "char<PERSON>t", "slice", "AudioBackendTester", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/UI Designolddd/jarvis-ui/src/components/Jarvis.js"], "sourcesContent": ["import { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\n\nconst Jarvis = ({ onExitToHome, onEnterChat }) => {\n  const [state, setState] = useState('startup');\n  const [micActive, setMicActive] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n\n\n  // Handle mic toggle with state switching\n  const handleMicToggle = (checked) => {\n    setMicActive(checked);\n\n    if (checked) {\n      // Turn on mic -> switch to listening mode\n      setState('listening');\n    } else {\n      // Turn off mic -> return to rest mode (unless processing/speaking)\n      if (state === 'listening') {\n        setState('rest');\n      }\n      // If it's thinking/speaking/startup, don't change state\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: { staggerChildren: 0.1 }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  // State content configuration\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      loop: true\n    },\n    rest: {\n      media: 'rest.mp4',\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      loop: true\n    },\n    listening: {\n      media: 'listening.gif',\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      loop: true\n    },\n    thinking: {\n      media: 'thinking.gif',\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      loop: true\n    },\n    speaking: {\n      media: 'speaking.gif',\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      loop: true\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\">\n      {/* System Audio Status - Top Right */}\n      <div className=\"fixed top-4 right-4 z-20\">\n        <SystemAudioStatus\n          isSystemAudio={isSystemAudio}\n          audioSource={audioSource}\n          audioLevel={audioLevel}\n        />\n      </div>\n\n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Centered Media Display */}\n      <div className=\"flex flex-col items-center justify-center space-y-8\">\n        <motion.div\n          className=\"relative w-96 h-96 rounded-full overflow-hidden\"\n        >\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={state}\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 1.05 }}\n              transition={{\n                duration: 0.6,\n                ease: [0.4, 0, 0.2, 1],\n                opacity: { duration: 0.4 }\n              }}\n              className=\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\"\n            >\n              {stateConfig[state].media.endsWith('.mp4') ? (\n                <video\n                  src={`/assets/${stateConfig[state].media}`}\n                  autoPlay\n                  muted\n                  loop\n                  playsInline\n                  className=\"w-full h-full object-cover rounded-full\"\n                  style={{ clipPath: 'circle(50% at 50% 50%)' }}\n                />\n              ) : (\n                <img\n                  src={`/assets/${stateConfig[state].media}`}\n                  alt={state}\n                  className={`w-full h-full object-cover rounded-full ${\n                    state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''\n                  }`}\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    imageRendering: 'auto',\n                    filter: 'contrast(1.1) brightness(1.05)',\n                    transition: 'all 0.3s ease-in-out'\n                  }}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Pulsing Halo */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full border-4 pointer-events-none\"\n            style={{ borderColor: stateConfig[state].color }}\n            animate={{\n              opacity: [0.3, 0.8, 0.3],\n              scale: [1, 1.1, 1],\n              boxShadow: `0 0 20px ${stateConfig[state].color}60`\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity\n            }}\n          />\n        </motion.div>\n\n        {/* Status Text */}\n        <motion.div\n          className=\"text-center space-y-4\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <motion.h1\n            className=\"text-4xl font-bold tracking-tighter\"\n            style={{\n              color: stateConfig[state].color\n            }}\n            animate={{\n              scale: 1 + (audioLevel * 0.1) + (beatDetected ? 0.05 : 0),\n              textShadow: `0 0 ${20 + (audioLevel * 30)}px currentColor, 0 0 ${40 + (audioLevel * 60)}px currentColor`\n            }}\n            variants={itemVariants}\n            transition={{\n              scale: { type: \"spring\", damping: 25, stiffness: 400 },\n              textShadow: { duration: 0.1 }\n            }}\n          >\n            {stateConfig[state].title}\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl text-cyan-200 font-light\"\n            variants={itemVariants}\n            animate={{\n              opacity: 0.8 + (midLevel * 0.2),\n              scale: 1 + (trebleLevel * 0.05)\n            }}\n            transition={{\n              opacity: { duration: 0.2 },\n              scale: { duration: 0.3 }\n            }}\n          >\n            {stateConfig[state].description}\n          </motion.p>\n        </motion.div>\n\n        {/* Mic Button - Center Bottom */}\n        <motion.div\n          className=\"mt-12\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <label className=\"container\">\n            <input\n              type=\"checkbox\"\n              checked={micActive}\n              onChange={(e) => handleMicToggle(e.target.checked)}\n            />\n            <div className=\"checkmark\">\n              {/* Mic Off Icon - with vertical cross line */}\n              <svg className=\"icon No\" viewBox=\"0 0 24 24\" fill=\"#dc6b6b\" style={{ color: '#dc6b6b' }}>\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"/>\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"/>\n                {/* Tilted cross line towards right */}\n                <line\n                  x1=\"6\" y1=\"4\"\n                  x2=\"18\" y2=\"20\"\n                  stroke=\"#dc6b6b\"\n                  strokeWidth=\"2.5\"\n                  strokeLinecap=\"round\"\n                />\n              </svg>\n              <span className=\"name No\">Mic Off</span>\n\n              {/* Mic On Icon - active listening */}\n              <svg className=\"icon Yes\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"/>\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"/>\n                {/* Sound waves indicator */}\n                <circle cx=\"12\" cy=\"8\" r=\"1\" opacity=\"0.6\">\n                  <animate attributeName=\"r\" values=\"1;2;1\" dur=\"1s\" repeatCount=\"indefinite\"/>\n                  <animate attributeName=\"opacity\" values=\"0.6;0.2;0.6\" dur=\"1s\" repeatCount=\"indefinite\"/>\n                </circle>\n                <circle cx=\"12\" cy=\"8\" r=\"3\" opacity=\"0.3\">\n                  <animate attributeName=\"r\" values=\"3;4;3\" dur=\"1.5s\" repeatCount=\"indefinite\"/>\n                  <animate attributeName=\"opacity\" values=\"0.3;0.1;0.3\" dur=\"1.5s\" repeatCount=\"indefinite\"/>\n                </circle>\n              </svg>\n              <span className=\"name Yes\">Listening</span>\n            </div>\n          </label>\n        </motion.div>\n\n        {/* Text Input - Below Mic Button */}\n        <motion.div\n          className=\"flex justify-center\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>\n            <input\n              type=\"text\"\n              className=\"input\"\n              placeholder=\"Type something intelligent (Press Enter to chat)\"\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onFocus={() => setIsInputFocused(true)}\n              onBlur={() => setIsInputFocused(false)}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter' && inputText.trim()) {\n                  // Handle text input submission - open chat screen\n                  const message = inputText.trim();\n                  setInputText('');\n                  onEnterChat(message);\n                }\n              }}\n            />\n            <div className=\"input-border\"></div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* State Control Menu - Bottom Left */}\n      <div className=\"absolute bottom-6 left-6\">\n        <motion.div\n          className=\"menu\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {Object.keys(stateConfig).map((s) => (\n            <motion.button\n              key={s}\n              className={`link ${state === s ? 'active' : ''}`}\n              style={{\n                backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',\n                border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: `0 0 12px ${stateConfig[s].color}50`\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setState(s)}\n              variants={itemVariants}\n              title={s.toUpperCase()}\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',\n                    color: 'white',\n                    fontSize: '10px'\n                  }}\n                >\n                  {s.charAt(0).toUpperCase()}\n                </div>\n              </div>\n              <span className=\"link-title\">\n                {s.charAt(0).toUpperCase() + s.slice(1)}\n              </span>\n            </motion.button>\n          ))}\n\n          {/* Home Button */}\n          {onExitToHome && (\n            <motion.button\n              className=\"link\"\n              style={{\n                backgroundColor: 'transparent',\n                border: '1px solid rgba(255, 255, 255, 0.3)'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={onExitToHome}\n              variants={itemVariants}\n              title=\"HOME\"\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                    color: 'black',\n                    fontSize: '10px'\n                  }}\n                >\n                  H\n                </div>\n              </div>\n              <span className=\"link-title\">Home</span>\n            </motion.button>\n          )}\n        </motion.div>\n      </div>\n\n      {/* Audio Backend Tester for comparing C# vs Python backends */}\n      <AudioBackendTester />\n\n    </div>\n  );\n};\n\nexport default Jarvis;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,iBAAiB,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,MAAM,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,SAAS,CAAC;EAC7C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;;EAG3D;EACA,MAAMkB,eAAe,GAAIC,OAAO,IAAK;IACnCN,YAAY,CAACM,OAAO,CAAC;IAErB,IAAIA,OAAO,EAAE;MACX;MACAR,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,MAAM;MACL;MACA,IAAID,KAAK,KAAK,WAAW,EAAE;QACzBC,QAAQ,CAAC,MAAM,CAAC;MAClB;MACA;IACF;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IACrC;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBL,MAAM,EAAE;MAAEM,CAAC,EAAE,EAAE;MAAEL,OAAO,EAAE;IAAE,CAAC;IAC7BC,OAAO,EAAE;MACPI,CAAC,EAAE,CAAC;MACJL,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEI,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE;MACPC,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,qBAAqB;MAClCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACJL,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,SAAS,EAAE;MACTN,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,wBAAwB;MACrCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDG,QAAQ,EAAE;MACRP,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDI,QAAQ,EAAE;MACRR,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR;EACF,CAAC;EAED,oBACE9B,OAAA;IAAKmC,SAAS,EAAC,8EAA8E;IAAAC,QAAA,gBAE3FpC,OAAA;MAAKmC,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvCpC,OAAA,CAACqC,iBAAiB;QAChBC,aAAa,EAAEA,aAAc;QAC7BC,WAAW,EAAEA,WAAY;QACzBC,UAAU,EAAEA;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5C,OAAA;MAAKmC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEpC,OAAA,CAACF,iBAAiB;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGN5C,OAAA;MAAKmC,SAAS,EAAC,qDAAqD;MAAAC,QAAA,gBAClEpC,OAAA,CAACJ,MAAM,CAACiD,GAAG;QACTV,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3DpC,OAAA,CAACH,eAAe;UAACiD,IAAI,EAAC,MAAM;UAAAV,QAAA,eAC1BpC,OAAA,CAACJ,MAAM,CAACiD,GAAG;YAETE,OAAO,EAAE;cAAE9B,OAAO,EAAE,CAAC;cAAE+B,KAAK,EAAE;YAAK,CAAE;YACrCC,OAAO,EAAE;cAAEhC,OAAO,EAAE,CAAC;cAAE+B,KAAK,EAAE;YAAE,CAAE;YAClCE,IAAI,EAAE;cAAEjC,OAAO,EAAE,CAAC;cAAE+B,KAAK,EAAE;YAAK,CAAE;YAClC7B,UAAU,EAAE;cACVI,QAAQ,EAAE,GAAG;cACb4B,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cACtBlC,OAAO,EAAE;gBAAEM,QAAQ,EAAE;cAAI;YAC3B,CAAE;YACFY,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAEpGZ,WAAW,CAACnB,KAAK,CAAC,CAACqB,KAAK,CAAC0B,QAAQ,CAAC,MAAM,CAAC,gBACxCpD,OAAA;cACEqD,GAAG,EAAE,WAAW7B,WAAW,CAACnB,KAAK,CAAC,CAACqB,KAAK,EAAG;cAC3C4B,QAAQ;cACRC,KAAK;cACLzB,IAAI;cACJ0B,WAAW;cACXrB,SAAS,EAAC,yCAAyC;cACnDsB,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAyB;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,gBAEF5C,OAAA;cACEqD,GAAG,EAAE,WAAW7B,WAAW,CAACnB,KAAK,CAAC,CAACqB,KAAK,EAAG;cAC3CiC,GAAG,EAAEtD,KAAM;cACX8B,SAAS,EAAE,2CACT9B,KAAK,KAAK,UAAU,GAAG,uCAAuC,GAAG,EAAE,EAClE;cACHoD,KAAK,EAAE;gBACLC,QAAQ,EAAE,wBAAwB;gBAClCE,cAAc,EAAE,MAAM;gBACtBC,MAAM,EAAE,gCAAgC;gBACxC1C,UAAU,EAAE;cACd;YAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACF,GAnCIvC,KAAK;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoCA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGlB5C,OAAA,CAACJ,MAAM,CAACiD,GAAG;UACTV,SAAS,EAAC,4DAA4D;UACtEsB,KAAK,EAAE;YAAEK,WAAW,EAAEtC,WAAW,CAACnB,KAAK,CAAC,CAACwB;UAAM,CAAE;UACjDoB,OAAO,EAAE;YACPhC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACxB+B,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YAClBe,SAAS,EAAE,YAAYvC,WAAW,CAACnB,KAAK,CAAC,CAACwB,KAAK;UACjD,CAAE;UACFV,UAAU,EAAE;YACVI,QAAQ,EAAE,CAAC;YACXyC,MAAM,EAAEC;UACV;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGb5C,OAAA,CAACJ,MAAM,CAACiD,GAAG;QACTV,SAAS,EAAC,uBAAuB;QACjC+B,QAAQ,EAAEnD,iBAAkB;QAC5BgC,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAb,QAAA,gBAEjBpC,OAAA,CAACJ,MAAM,CAACuE,EAAE;UACRhC,SAAS,EAAC,qCAAqC;UAC/CsB,KAAK,EAAE;YACL5B,KAAK,EAAEL,WAAW,CAACnB,KAAK,CAAC,CAACwB;UAC5B,CAAE;UACFoB,OAAO,EAAE;YACPD,KAAK,EAAE,CAAC,GAAIR,UAAU,GAAG,GAAI,IAAI4B,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;YACzDC,UAAU,EAAE,OAAO,EAAE,GAAI7B,UAAU,GAAG,EAAG,wBAAwB,EAAE,GAAIA,UAAU,GAAG,EAAG;UACzF,CAAE;UACF0B,QAAQ,EAAE7C,YAAa;UACvBF,UAAU,EAAE;YACV6B,KAAK,EAAE;cAAEsB,IAAI,EAAE,QAAQ;cAAEC,OAAO,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAI,CAAC;YACtDH,UAAU,EAAE;cAAE9C,QAAQ,EAAE;YAAI;UAC9B,CAAE;UAAAa,QAAA,EAEDZ,WAAW,CAACnB,KAAK,CAAC,CAACsB;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEZ5C,OAAA,CAACJ,MAAM,CAAC6E,CAAC;UACPtC,SAAS,EAAC,kCAAkC;UAC5C+B,QAAQ,EAAE7C,YAAa;UACvB4B,OAAO,EAAE;YACPhC,OAAO,EAAE,GAAG,GAAIyD,QAAQ,GAAG,GAAI;YAC/B1B,KAAK,EAAE,CAAC,GAAI2B,WAAW,GAAG;UAC5B,CAAE;UACFxD,UAAU,EAAE;YACVF,OAAO,EAAE;cAAEM,QAAQ,EAAE;YAAI,CAAC;YAC1ByB,KAAK,EAAE;cAAEzB,QAAQ,EAAE;YAAI;UACzB,CAAE;UAAAa,QAAA,EAEDZ,WAAW,CAACnB,KAAK,CAAC,CAACuB;QAAW;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGb5C,OAAA,CAACJ,MAAM,CAACiD,GAAG;QACTV,SAAS,EAAC,OAAO;QACjB+B,QAAQ,EAAE7C,YAAa;QACvB0B,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAb,QAAA,eAEjBpC,OAAA;UAAOmC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAC1BpC,OAAA;YACEsE,IAAI,EAAC,UAAU;YACfxD,OAAO,EAAEP,SAAU;YACnBqE,QAAQ,EAAGC,CAAC,IAAKhE,eAAe,CAACgE,CAAC,CAACC,MAAM,CAAChE,OAAO;UAAE;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACF5C,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBpC,OAAA;cAAKmC,SAAS,EAAC,SAAS;cAAC4C,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,SAAS;cAACvB,KAAK,EAAE;gBAAE5B,KAAK,EAAE;cAAU,CAAE;cAAAO,QAAA,gBACtFpC,OAAA;gBAAMiF,CAAC,EAAC;cAA8E;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACxF5C,OAAA;gBAAMiF,CAAC,EAAC;cAAsG;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAEhH5C,OAAA;gBACEkF,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBACbC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBACfC,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAC,KAAK;gBACjBC,aAAa,EAAC;cAAO;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5C,OAAA;cAAMmC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGxC5C,OAAA;cAAKmC,SAAS,EAAC,UAAU;cAAC4C,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAA5C,QAAA,gBAC/DpC,OAAA;gBAAMiF,CAAC,EAAC;cAA8E;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACxF5C,OAAA;gBAAMiF,CAAC,EAAC;cAAsG;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAEhH5C,OAAA;gBAAQyF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAAC1E,OAAO,EAAC,KAAK;gBAAAmB,QAAA,gBACxCpC,OAAA;kBAAS4F,aAAa,EAAC,GAAG;kBAACC,MAAM,EAAC,OAAO;kBAACC,GAAG,EAAC,IAAI;kBAACC,WAAW,EAAC;gBAAY;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC7E5C,OAAA;kBAAS4F,aAAa,EAAC,SAAS;kBAACC,MAAM,EAAC,aAAa;kBAACC,GAAG,EAAC,IAAI;kBAACC,WAAW,EAAC;gBAAY;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACT5C,OAAA;gBAAQyF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAAC1E,OAAO,EAAC,KAAK;gBAAAmB,QAAA,gBACxCpC,OAAA;kBAAS4F,aAAa,EAAC,GAAG;kBAACC,MAAM,EAAC,OAAO;kBAACC,GAAG,EAAC,MAAM;kBAACC,WAAW,EAAC;gBAAY;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/E5C,OAAA;kBAAS4F,aAAa,EAAC,SAAS;kBAACC,MAAM,EAAC,aAAa;kBAACC,GAAG,EAAC,MAAM;kBAACC,WAAW,EAAC;gBAAY;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN5C,OAAA;cAAMmC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGb5C,OAAA,CAACJ,MAAM,CAACiD,GAAG;QACTV,SAAS,EAAC,qBAAqB;QAC/B+B,QAAQ,EAAE7C,YAAa;QACvB0B,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAb,QAAA,eAEjBpC,OAAA;UAAKmC,SAAS,EAAE,gBAAgBxB,cAAc,GAAG,SAAS,GAAG,EAAE,EAAG;UAAAyB,QAAA,gBAChEpC,OAAA;YACEsE,IAAI,EAAC,MAAM;YACXnC,SAAS,EAAC,OAAO;YACjB6D,WAAW,EAAC,kDAAkD;YAC9DC,KAAK,EAAExF,SAAU;YACjBmE,QAAQ,EAAGC,CAAC,IAAKnE,YAAY,CAACmE,CAAC,CAACC,MAAM,CAACmB,KAAK,CAAE;YAC9CC,OAAO,EAAEA,CAAA,KAAMtF,iBAAiB,CAAC,IAAI,CAAE;YACvCuF,MAAM,EAAEA,CAAA,KAAMvF,iBAAiB,CAAC,KAAK,CAAE;YACvCwF,SAAS,EAAGvB,CAAC,IAAK;cAChB,IAAIA,CAAC,CAACwB,GAAG,KAAK,OAAO,IAAI5F,SAAS,CAAC6F,IAAI,CAAC,CAAC,EAAE;gBACzC;gBACA,MAAMC,OAAO,GAAG9F,SAAS,CAAC6F,IAAI,CAAC,CAAC;gBAChC5F,YAAY,CAAC,EAAE,CAAC;gBAChBP,WAAW,CAACoG,OAAO,CAAC;cACtB;YACF;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF5C,OAAA;YAAKmC,SAAS,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN5C,OAAA;MAAKmC,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvCpC,OAAA,CAACJ,MAAM,CAACiD,GAAG;QACTV,SAAS,EAAC,MAAM;QAChB+B,QAAQ,EAAEnD,iBAAkB;QAC5BgC,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAb,QAAA,GAEhBoE,MAAM,CAACC,IAAI,CAACjF,WAAW,CAAC,CAACkF,GAAG,CAAEC,CAAC,iBAC9B3G,OAAA,CAACJ,MAAM,CAACgH,MAAM;UAEZzE,SAAS,EAAE,QAAQ9B,KAAK,KAAKsG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjDlD,KAAK,EAAE;YACLoD,eAAe,EAAExG,KAAK,KAAKsG,CAAC,GAAGnF,WAAW,CAACmF,CAAC,CAAC,CAAC9E,KAAK,GAAG,IAAI,GAAG,aAAa;YAC1EiF,MAAM,EAAEzG,KAAK,KAAKsG,CAAC,GAAG,aAAanF,WAAW,CAACmF,CAAC,CAAC,CAAC9E,KAAK,IAAI,GAAG;UAChE,CAAE;UACFkF,UAAU,EAAE;YACV/D,KAAK,EAAE,IAAI;YACXe,SAAS,EAAE,YAAYvC,WAAW,CAACmF,CAAC,CAAC,CAAC9E,KAAK;UAC7C,CAAE;UACFmF,QAAQ,EAAE;YAAEhE,KAAK,EAAE;UAAK,CAAE;UAC1BiE,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAACqG,CAAC,CAAE;UAC3BzC,QAAQ,EAAE7C,YAAa;UACvBM,KAAK,EAAEgF,CAAC,CAACO,WAAW,CAAC,CAAE;UAAA9E,QAAA,gBAEvBpC,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpC,OAAA;cACEmC,SAAS,EAAC,yEAAyE;cACnFsB,KAAK,EAAE;gBACLoD,eAAe,EAAExG,KAAK,KAAKsG,CAAC,GAAGnF,WAAW,CAACmF,CAAC,CAAC,CAAC9E,KAAK,GAAG,wBAAwB;gBAC9EA,KAAK,EAAE,OAAO;gBACdsF,QAAQ,EAAE;cACZ,CAAE;cAAA/E,QAAA,EAEDuE,CAAC,CAACS,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC;YAAC;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5C,OAAA;YAAMmC,SAAS,EAAC,YAAY;YAAAC,QAAA,EACzBuE,CAAC,CAACS,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,GAAGP,CAAC,CAACU,KAAK,CAAC,CAAC;UAAC;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA,GA7BF+D,CAAC;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BO,CAChB,CAAC,EAGD1C,YAAY,iBACXF,OAAA,CAACJ,MAAM,CAACgH,MAAM;UACZzE,SAAS,EAAC,MAAM;UAChBsB,KAAK,EAAE;YACLoD,eAAe,EAAE,aAAa;YAC9BC,MAAM,EAAE;UACV,CAAE;UACFC,UAAU,EAAE;YACV/D,KAAK,EAAE,IAAI;YACXe,SAAS,EAAE;UACb,CAAE;UACFiD,QAAQ,EAAE;YAAEhE,KAAK,EAAE;UAAK,CAAE;UAC1BiE,OAAO,EAAE/G,YAAa;UACtBgE,QAAQ,EAAE7C,YAAa;UACvBM,KAAK,EAAC,MAAM;UAAAS,QAAA,gBAEZpC,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpC,OAAA;cACEmC,SAAS,EAAC,yEAAyE;cACnFsB,KAAK,EAAE;gBACLoD,eAAe,EAAE,0BAA0B;gBAC3ChF,KAAK,EAAE,OAAO;gBACdsF,QAAQ,EAAE;cACZ,CAAE;cAAA/E,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5C,OAAA;YAAMmC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN5C,OAAA,CAACsH,kBAAkB;MAAA7E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEnB,CAAC;AAEV,CAAC;AAACxC,EAAA,CAxWIH,MAAM;AAAAsH,EAAA,GAANtH,MAAM;AA0WZ,eAAeA,MAAM;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}