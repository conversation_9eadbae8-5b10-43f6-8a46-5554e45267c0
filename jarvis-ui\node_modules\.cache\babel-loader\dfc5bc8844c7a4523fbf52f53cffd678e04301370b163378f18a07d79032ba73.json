{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\UI Designolddd\\\\jarvis-ui\\\\src\\\\components\\\\Jarvis.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport SystemAudioStatus from './ui/SystemAudioStatus';\nimport usePythonAudioBackend from '../hooks/usePythonAudioBackend';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Jarvis = ({\n  onExitToHome,\n  onEnterChat\n}) => {\n  _s();\n  const [state, setState] = useState('startup');\n  const [micActive, setMicActive] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n  // C# NVIDIA HDMI Audio Backend - Primary audio source\n  const {\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    isAudioActive,\n    beatDetected,\n    isSystemAudio,\n    audioSource\n  } = usePythonAudioBackend({\n    enabled: true,\n    serverUrl: 'ws://localhost:8765',\n    // C# backend runs on same port\n    beatThreshold: 0.15,\n    updateInterval: 16\n  });\n\n  // Handle mic toggle with state switching\n  const handleMicToggle = checked => {\n    setMicActive(checked);\n    if (checked) {\n      // Turn on mic -> switch to listening mode\n      setState('listening');\n    } else {\n      // Turn off mic -> return to rest mode (unless processing/speaking)\n      if (state === 'listening') {\n        setState('rest');\n      }\n      // If it's thinking/speaking/startup, don't change state\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      y: 20,\n      opacity: 0\n    },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  // State content configuration\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      loop: true\n    },\n    rest: {\n      media: 'rest.mp4',\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      loop: true\n    },\n    listening: {\n      media: 'listening.gif',\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      loop: true\n    },\n    thinking: {\n      media: 'thinking.gif',\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      loop: true\n    },\n    speaking: {\n      media: 'speaking.gif',\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      loop: true\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-20\",\n      children: /*#__PURE__*/_jsxDEV(SystemAudioStatus, {\n        isSystemAudio: isSystemAudio,\n        audioSource: audioSource,\n        audioLevel: audioLevel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",\n      children: /*#__PURE__*/_jsxDEV(ExpandableSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center justify-center space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative w-96 h-96 rounded-full overflow-hidden\",\n        animate: {\n          scale: 1 + audioLevel * 0.2 + (beatDetected ? 0.15 : 0),\n          // Audio-reactive scaling\n          filter: `brightness(${1 + audioLevel * 0.3}) contrast(${1 + bassLevel * 0.2})`\n        },\n        transition: {\n          scale: {\n            type: \"spring\",\n            damping: 20,\n            stiffness: 300\n          },\n          filter: {\n            duration: 0.1\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.95\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 1.05\n            },\n            transition: {\n              duration: 0.6,\n              ease: [0.4, 0, 0.2, 1],\n              opacity: {\n                duration: 0.4\n              }\n            },\n            className: \"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\",\n            children: stateConfig[state].media.endsWith('.mp4') ? /*#__PURE__*/_jsxDEV(\"video\", {\n              src: `/assets/${stateConfig[state].media}`,\n              autoPlay: true,\n              muted: true,\n              loop: true,\n              playsInline: true,\n              className: \"w-full h-full object-cover rounded-full\",\n              style: {\n                clipPath: 'circle(50% at 50% 50%)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `/assets/${stateConfig[state].media}`,\n              alt: state,\n              className: `w-full h-full object-cover rounded-full ${state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''}`,\n              style: {\n                clipPath: 'circle(50% at 50% 50%)',\n                imageRendering: 'auto',\n                filter: 'contrast(1.1) brightness(1.05)',\n                transition: 'all 0.3s ease-in-out'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)\n          }, state, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute inset-0 rounded-full border-4 pointer-events-none\",\n          style: {\n            borderColor: stateConfig[state].color\n          },\n          animate: {\n            opacity: [0.3 + audioLevel * 0.4, 0.8 + audioLevel * 0.2, 0.3 + audioLevel * 0.4],\n            scale: [1 + bassLevel * 0.1, 1.1 + audioLevel * 0.2 + (beatDetected ? 0.3 : 0), 1 + bassLevel * 0.1],\n            borderWidth: 4 + audioLevel * 6,\n            boxShadow: `0 0 ${20 + audioLevel * 40}px ${stateConfig[state].color}${Math.floor(60 + audioLevel * 40).toString(16)}`\n          },\n          transition: {\n            duration: Math.max(0.5, 2 - audioLevel * 1.5),\n            // Faster pulsing with more audio\n            repeat: Infinity,\n            borderWidth: {\n              duration: 0.1\n            },\n            boxShadow: {\n              duration: 0.1\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center space-y-4\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          className: \"text-4xl font-bold tracking-tighter\",\n          style: {\n            color: stateConfig[state].color\n          },\n          animate: {\n            scale: 1 + audioLevel * 0.1 + (beatDetected ? 0.05 : 0),\n            textShadow: `0 0 ${20 + audioLevel * 30}px currentColor, 0 0 ${40 + audioLevel * 60}px currentColor`\n          },\n          variants: itemVariants,\n          transition: {\n            scale: {\n              type: \"spring\",\n              damping: 25,\n              stiffness: 400\n            },\n            textShadow: {\n              duration: 0.1\n            }\n          },\n          children: stateConfig[state].title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          className: \"text-xl text-cyan-200 font-light\",\n          variants: itemVariants,\n          animate: {\n            opacity: 0.8 + midLevel * 0.2,\n            scale: 1 + trebleLevel * 0.05\n          },\n          transition: {\n            opacity: {\n              duration: 0.2\n            },\n            scale: {\n              duration: 0.3\n            }\n          },\n          children: stateConfig[state].description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"mt-12\",\n        variants: itemVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: micActive,\n            onChange: e => handleMicToggle(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"checkmark\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"icon No\",\n              viewBox: \"0 0 24 24\",\n              fill: \"#dc6b6b\",\n              style: {\n                color: '#dc6b6b'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"6\",\n                y1: \"4\",\n                x2: \"18\",\n                y2: \"20\",\n                stroke: \"#dc6b6b\",\n                strokeWidth: \"2.5\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"name No\",\n              children: \"Mic Off\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"icon Yes\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"8\",\n                r: \"1\",\n                opacity: \"0.6\",\n                children: [/*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"r\",\n                  values: \"1;2;1\",\n                  dur: \"1s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"opacity\",\n                  values: \"0.6;0.2;0.6\",\n                  dur: \"1s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"8\",\n                r: \"3\",\n                opacity: \"0.3\",\n                children: [/*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"r\",\n                  values: \"3;4;3\",\n                  dur: \"1.5s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"opacity\",\n                  values: \"0.3;0.1;0.3\",\n                  dur: \"1.5s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"name Yes\",\n              children: \"Listening\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"flex justify-center\",\n        variants: itemVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `form-control ${isInputFocused ? 'focused' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"input\",\n            placeholder: \"Type something intelligent (Press Enter to chat)\",\n            value: inputText,\n            onChange: e => setInputText(e.target.value),\n            onFocus: () => setIsInputFocused(true),\n            onBlur: () => setIsInputFocused(false),\n            onKeyDown: e => {\n              if (e.key === 'Enter' && inputText.trim()) {\n                // Handle text input submission - open chat screen\n                const message = inputText.trim();\n                setInputText('');\n                onEnterChat(message);\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-6 left-6\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"menu\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [Object.keys(stateConfig).map(s => /*#__PURE__*/_jsxDEV(motion.button, {\n          className: `link ${state === s ? 'active' : ''}`,\n          style: {\n            backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',\n            border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'\n          },\n          whileHover: {\n            scale: 1.05,\n            boxShadow: `0 0 12px ${stateConfig[s].color}50`\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setState(s),\n          variants: itemVariants,\n          title: s.toUpperCase(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"link-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",\n              style: {\n                backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',\n                color: 'white',\n                fontSize: '10px'\n              },\n              children: s.charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"link-title\",\n            children: s.charAt(0).toUpperCase() + s.slice(1)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)]\n        }, s, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)), onExitToHome && /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"link\",\n          style: {\n            backgroundColor: 'transparent',\n            border: '1px solid rgba(255, 255, 255, 0.3)'\n          },\n          whileHover: {\n            scale: 1.05,\n            boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: onExitToHome,\n          variants: itemVariants,\n          title: \"HOME\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"link-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",\n              style: {\n                backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                color: 'black',\n                fontSize: '10px'\n              },\n              children: \"H\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"link-title\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(Jarvis, \"NoheMK7VrI7y0xL25HPTpqGWNdg=\", false, function () {\n  return [usePythonAudioBackend];\n});\n_c = Jarvis;\nexport default Jarvis;\nvar _c;\n$RefreshReg$(_c, \"Jarvis\");", "map": {"version": 3, "names": ["useState", "motion", "AnimatePresence", "ExpandableSidebar", "SystemAudioStatus", "usePythonAudioBackend", "jsxDEV", "_jsxDEV", "<PERSON>", "onExitToHome", "onEnterChat", "_s", "state", "setState", "micActive", "setMicActive", "inputText", "setInputText", "isInputFocused", "setIsInputFocused", "audioLevel", "bassLevel", "midLevel", "trebleLevel", "isAudioActive", "beatDetected", "isSystemAudio", "audioSource", "enabled", "serverUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateInterval", "handleMicToggle", "checked", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "stateConfig", "startup", "media", "title", "description", "color", "loop", "rest", "listening", "thinking", "speaking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "animate", "scale", "filter", "type", "damping", "stiffness", "mode", "initial", "exit", "ease", "endsWith", "src", "autoPlay", "muted", "playsInline", "style", "clipPath", "alt", "imageRendering", "borderColor", "borderWidth", "boxShadow", "Math", "floor", "toString", "max", "repeat", "Infinity", "variants", "h1", "textShadow", "p", "onChange", "e", "target", "viewBox", "fill", "d", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "strokeLinecap", "cx", "cy", "r", "attributeName", "values", "dur", "repeatCount", "placeholder", "value", "onFocus", "onBlur", "onKeyDown", "key", "trim", "message", "Object", "keys", "map", "s", "button", "backgroundColor", "border", "whileHover", "whileTap", "onClick", "toUpperCase", "fontSize", "char<PERSON>t", "slice", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/UI Designolddd/jarvis-ui/src/components/Jarvis.js"], "sourcesContent": ["import { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport SystemAudioStatus from './ui/SystemAudioStatus';\nimport usePythonAudioBackend from '../hooks/usePythonAudioBackend';\n\nconst Jarvis = ({ onExitToHome, onEnterChat }) => {\n  const [state, setState] = useState('startup');\n  const [micActive, setMicActive] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n  // C# NVIDIA HDMI Audio Backend - Primary audio source\n  const {\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    isAudioActive,\n    beatDetected,\n    isSystemAudio,\n    audioSource\n  } = usePythonAudioBackend({\n    enabled: true,\n    serverUrl: 'ws://localhost:8765', // C# backend runs on same port\n    beatThreshold: 0.15,\n    updateInterval: 16\n  });\n\n  // Handle mic toggle with state switching\n  const handleMicToggle = (checked) => {\n    setMicActive(checked);\n\n    if (checked) {\n      // Turn on mic -> switch to listening mode\n      setState('listening');\n    } else {\n      // Turn off mic -> return to rest mode (unless processing/speaking)\n      if (state === 'listening') {\n        setState('rest');\n      }\n      // If it's thinking/speaking/startup, don't change state\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: { staggerChildren: 0.1 }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  // State content configuration\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      loop: true\n    },\n    rest: {\n      media: 'rest.mp4',\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      loop: true\n    },\n    listening: {\n      media: 'listening.gif',\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      loop: true\n    },\n    thinking: {\n      media: 'thinking.gif',\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      loop: true\n    },\n    speaking: {\n      media: 'speaking.gif',\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      loop: true\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\">\n      {/* System Audio Status - Top Right */}\n      <div className=\"fixed top-4 right-4 z-20\">\n        <SystemAudioStatus\n          isSystemAudio={isSystemAudio}\n          audioSource={audioSource}\n          audioLevel={audioLevel}\n        />\n      </div>\n\n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Centered Media Display */}\n      <div className=\"flex flex-col items-center justify-center space-y-8\">\n        <motion.div\n          className=\"relative w-96 h-96 rounded-full overflow-hidden\"\n          animate={{\n            scale: 1 + (audioLevel * 0.2) + (beatDetected ? 0.15 : 0), // Audio-reactive scaling\n            filter: `brightness(${1 + (audioLevel * 0.3)}) contrast(${1 + (bassLevel * 0.2)})`,\n          }}\n          transition={{\n            scale: { type: \"spring\", damping: 20, stiffness: 300 },\n            filter: { duration: 0.1 }\n          }}\n        >\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={state}\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 1.05 }}\n              transition={{\n                duration: 0.6,\n                ease: [0.4, 0, 0.2, 1],\n                opacity: { duration: 0.4 }\n              }}\n              className=\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\"\n            >\n              {stateConfig[state].media.endsWith('.mp4') ? (\n                <video\n                  src={`/assets/${stateConfig[state].media}`}\n                  autoPlay\n                  muted\n                  loop\n                  playsInline\n                  className=\"w-full h-full object-cover rounded-full\"\n                  style={{ clipPath: 'circle(50% at 50% 50%)' }}\n                />\n              ) : (\n                <img\n                  src={`/assets/${stateConfig[state].media}`}\n                  alt={state}\n                  className={`w-full h-full object-cover rounded-full ${\n                    state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''\n                  }`}\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    imageRendering: 'auto',\n                    filter: 'contrast(1.1) brightness(1.05)',\n                    transition: 'all 0.3s ease-in-out'\n                  }}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Pulsing Halo - Audio Reactive */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full border-4 pointer-events-none\"\n            style={{ borderColor: stateConfig[state].color }}\n            animate={{\n              opacity: [0.3 + (audioLevel * 0.4), 0.8 + (audioLevel * 0.2), 0.3 + (audioLevel * 0.4)],\n              scale: [1 + (bassLevel * 0.1), 1.1 + (audioLevel * 0.2) + (beatDetected ? 0.3 : 0), 1 + (bassLevel * 0.1)],\n              borderWidth: 4 + (audioLevel * 6),\n              boxShadow: `0 0 ${20 + (audioLevel * 40)}px ${stateConfig[state].color}${Math.floor(60 + (audioLevel * 40)).toString(16)}`\n            }}\n            transition={{\n              duration: Math.max(0.5, 2 - (audioLevel * 1.5)), // Faster pulsing with more audio\n              repeat: Infinity,\n              borderWidth: { duration: 0.1 },\n              boxShadow: { duration: 0.1 }\n            }}\n          />\n        </motion.div>\n\n        {/* Status Text */}\n        <motion.div\n          className=\"text-center space-y-4\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <motion.h1\n            className=\"text-4xl font-bold tracking-tighter\"\n            style={{\n              color: stateConfig[state].color\n            }}\n            animate={{\n              scale: 1 + (audioLevel * 0.1) + (beatDetected ? 0.05 : 0),\n              textShadow: `0 0 ${20 + (audioLevel * 30)}px currentColor, 0 0 ${40 + (audioLevel * 60)}px currentColor`\n            }}\n            variants={itemVariants}\n            transition={{\n              scale: { type: \"spring\", damping: 25, stiffness: 400 },\n              textShadow: { duration: 0.1 }\n            }}\n          >\n            {stateConfig[state].title}\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl text-cyan-200 font-light\"\n            variants={itemVariants}\n            animate={{\n              opacity: 0.8 + (midLevel * 0.2),\n              scale: 1 + (trebleLevel * 0.05)\n            }}\n            transition={{\n              opacity: { duration: 0.2 },\n              scale: { duration: 0.3 }\n            }}\n          >\n            {stateConfig[state].description}\n          </motion.p>\n        </motion.div>\n\n        {/* Mic Button - Center Bottom */}\n        <motion.div\n          className=\"mt-12\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <label className=\"container\">\n            <input\n              type=\"checkbox\"\n              checked={micActive}\n              onChange={(e) => handleMicToggle(e.target.checked)}\n            />\n            <div className=\"checkmark\">\n              {/* Mic Off Icon - with vertical cross line */}\n              <svg className=\"icon No\" viewBox=\"0 0 24 24\" fill=\"#dc6b6b\" style={{ color: '#dc6b6b' }}>\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"/>\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"/>\n                {/* Tilted cross line towards right */}\n                <line\n                  x1=\"6\" y1=\"4\"\n                  x2=\"18\" y2=\"20\"\n                  stroke=\"#dc6b6b\"\n                  strokeWidth=\"2.5\"\n                  strokeLinecap=\"round\"\n                />\n              </svg>\n              <span className=\"name No\">Mic Off</span>\n\n              {/* Mic On Icon - active listening */}\n              <svg className=\"icon Yes\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"/>\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"/>\n                {/* Sound waves indicator */}\n                <circle cx=\"12\" cy=\"8\" r=\"1\" opacity=\"0.6\">\n                  <animate attributeName=\"r\" values=\"1;2;1\" dur=\"1s\" repeatCount=\"indefinite\"/>\n                  <animate attributeName=\"opacity\" values=\"0.6;0.2;0.6\" dur=\"1s\" repeatCount=\"indefinite\"/>\n                </circle>\n                <circle cx=\"12\" cy=\"8\" r=\"3\" opacity=\"0.3\">\n                  <animate attributeName=\"r\" values=\"3;4;3\" dur=\"1.5s\" repeatCount=\"indefinite\"/>\n                  <animate attributeName=\"opacity\" values=\"0.3;0.1;0.3\" dur=\"1.5s\" repeatCount=\"indefinite\"/>\n                </circle>\n              </svg>\n              <span className=\"name Yes\">Listening</span>\n            </div>\n          </label>\n        </motion.div>\n\n        {/* Text Input - Below Mic Button */}\n        <motion.div\n          className=\"flex justify-center\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>\n            <input\n              type=\"text\"\n              className=\"input\"\n              placeholder=\"Type something intelligent (Press Enter to chat)\"\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onFocus={() => setIsInputFocused(true)}\n              onBlur={() => setIsInputFocused(false)}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter' && inputText.trim()) {\n                  // Handle text input submission - open chat screen\n                  const message = inputText.trim();\n                  setInputText('');\n                  onEnterChat(message);\n                }\n              }}\n            />\n            <div className=\"input-border\"></div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* State Control Menu - Bottom Left */}\n      <div className=\"absolute bottom-6 left-6\">\n        <motion.div\n          className=\"menu\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {Object.keys(stateConfig).map((s) => (\n            <motion.button\n              key={s}\n              className={`link ${state === s ? 'active' : ''}`}\n              style={{\n                backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',\n                border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: `0 0 12px ${stateConfig[s].color}50`\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setState(s)}\n              variants={itemVariants}\n              title={s.toUpperCase()}\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',\n                    color: 'white',\n                    fontSize: '10px'\n                  }}\n                >\n                  {s.charAt(0).toUpperCase()}\n                </div>\n              </div>\n              <span className=\"link-title\">\n                {s.charAt(0).toUpperCase() + s.slice(1)}\n              </span>\n            </motion.button>\n          ))}\n\n          {/* Home Button */}\n          {onExitToHome && (\n            <motion.button\n              className=\"link\"\n              style={{\n                backgroundColor: 'transparent',\n                border: '1px solid rgba(255, 255, 255, 0.3)'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={onExitToHome}\n              variants={itemVariants}\n              title=\"HOME\"\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                    color: 'black',\n                    fontSize: '10px'\n                  }}\n                >\n                  H\n                </div>\n              </div>\n              <span className=\"link-title\">Home</span>\n            </motion.button>\n          )}\n        </motion.div>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default Jarvis;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,qBAAqB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,SAAS,CAAC;EAC7C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3D;EACA,MAAM;IACJoB,UAAU;IACVC,SAAS;IACTC,QAAQ;IACRC,WAAW;IACXC,aAAa;IACbC,YAAY;IACZC,aAAa;IACbC;EACF,CAAC,GAAGtB,qBAAqB,CAAC;IACxBuB,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,qBAAqB;IAAE;IAClCC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAIC,OAAO,IAAK;IACnClB,YAAY,CAACkB,OAAO,CAAC;IAErB,IAAIA,OAAO,EAAE;MACX;MACApB,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,MAAM;MACL;MACA,IAAID,KAAK,KAAK,WAAW,EAAE;QACzBC,QAAQ,CAAC,MAAM,CAAC;MAClB;MACA;IACF;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IACrC;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBL,MAAM,EAAE;MAAEM,CAAC,EAAE,EAAE;MAAEL,OAAO,EAAE;IAAE,CAAC;IAC7BC,OAAO,EAAE;MACPI,CAAC,EAAE,CAAC;MACJL,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEI,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE;MACPC,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,qBAAqB;MAClCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACJL,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,SAAS,EAAE;MACTN,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,wBAAwB;MACrCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDG,QAAQ,EAAE;MACRP,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDI,QAAQ,EAAE;MACRR,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR;EACF,CAAC;EAED,oBACE1C,OAAA;IAAK+C,SAAS,EAAC,8EAA8E;IAAAC,QAAA,gBAE3FhD,OAAA;MAAK+C,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvChD,OAAA,CAACH,iBAAiB;QAChBsB,aAAa,EAAEA,aAAc;QAC7BC,WAAW,EAAEA,WAAY;QACzBP,UAAU,EAAEA;MAAW;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpD,OAAA;MAAK+C,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEhD,OAAA,CAACJ,iBAAiB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGNpD,OAAA;MAAK+C,SAAS,EAAC,qDAAqD;MAAAC,QAAA,gBAClEhD,OAAA,CAACN,MAAM,CAAC2D,GAAG;QACTN,SAAS,EAAC,iDAAiD;QAC3DO,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,GAAI1C,UAAU,GAAG,GAAI,IAAIK,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;UAAE;UAC3DsC,MAAM,EAAE,cAAc,CAAC,GAAI3C,UAAU,GAAG,GAAI,cAAc,CAAC,GAAIC,SAAS,GAAG,GAAI;QACjF,CAAE;QACFiB,UAAU,EAAE;UACVwB,KAAK,EAAE;YAAEE,IAAI,EAAE,QAAQ;YAAEC,OAAO,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAI,CAAC;UACtDH,MAAM,EAAE;YAAErB,QAAQ,EAAE;UAAI;QAC1B,CAAE;QAAAa,QAAA,gBAEFhD,OAAA,CAACL,eAAe;UAACiE,IAAI,EAAC,MAAM;UAAAZ,QAAA,eAC1BhD,OAAA,CAACN,MAAM,CAAC2D,GAAG;YAETQ,OAAO,EAAE;cAAEhC,OAAO,EAAE,CAAC;cAAE0B,KAAK,EAAE;YAAK,CAAE;YACrCD,OAAO,EAAE;cAAEzB,OAAO,EAAE,CAAC;cAAE0B,KAAK,EAAE;YAAE,CAAE;YAClCO,IAAI,EAAE;cAAEjC,OAAO,EAAE,CAAC;cAAE0B,KAAK,EAAE;YAAK,CAAE;YAClCxB,UAAU,EAAE;cACVI,QAAQ,EAAE,GAAG;cACb4B,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cACtBlC,OAAO,EAAE;gBAAEM,QAAQ,EAAE;cAAI;YAC3B,CAAE;YACFY,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAEpGZ,WAAW,CAAC/B,KAAK,CAAC,CAACiC,KAAK,CAAC0B,QAAQ,CAAC,MAAM,CAAC,gBACxChE,OAAA;cACEiE,GAAG,EAAE,WAAW7B,WAAW,CAAC/B,KAAK,CAAC,CAACiC,KAAK,EAAG;cAC3C4B,QAAQ;cACRC,KAAK;cACLzB,IAAI;cACJ0B,WAAW;cACXrB,SAAS,EAAC,yCAAyC;cACnDsB,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAyB;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,gBAEFpD,OAAA;cACEiE,GAAG,EAAE,WAAW7B,WAAW,CAAC/B,KAAK,CAAC,CAACiC,KAAK,EAAG;cAC3CiC,GAAG,EAAElE,KAAM;cACX0C,SAAS,EAAE,2CACT1C,KAAK,KAAK,UAAU,GAAG,uCAAuC,GAAG,EAAE,EAClE;cACHgE,KAAK,EAAE;gBACLC,QAAQ,EAAE,wBAAwB;gBAClCE,cAAc,EAAE,MAAM;gBACtBhB,MAAM,EAAE,gCAAgC;gBACxCzB,UAAU,EAAE;cACd;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACF,GAnCI/C,KAAK;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoCA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGlBpD,OAAA,CAACN,MAAM,CAAC2D,GAAG;UACTN,SAAS,EAAC,4DAA4D;UACtEsB,KAAK,EAAE;YAAEI,WAAW,EAAErC,WAAW,CAAC/B,KAAK,CAAC,CAACoC;UAAM,CAAE;UACjDa,OAAO,EAAE;YACPzB,OAAO,EAAE,CAAC,GAAG,GAAIhB,UAAU,GAAG,GAAI,EAAE,GAAG,GAAIA,UAAU,GAAG,GAAI,EAAE,GAAG,GAAIA,UAAU,GAAG,GAAI,CAAC;YACvF0C,KAAK,EAAE,CAAC,CAAC,GAAIzC,SAAS,GAAG,GAAI,EAAE,GAAG,GAAID,UAAU,GAAG,GAAI,IAAIK,YAAY,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIJ,SAAS,GAAG,GAAI,CAAC;YAC1G4D,WAAW,EAAE,CAAC,GAAI7D,UAAU,GAAG,CAAE;YACjC8D,SAAS,EAAE,OAAO,EAAE,GAAI9D,UAAU,GAAG,EAAG,MAAMuB,WAAW,CAAC/B,KAAK,CAAC,CAACoC,KAAK,GAAGmC,IAAI,CAACC,KAAK,CAAC,EAAE,GAAIhE,UAAU,GAAG,EAAG,CAAC,CAACiE,QAAQ,CAAC,EAAE,CAAC;UAC1H,CAAE;UACF/C,UAAU,EAAE;YACVI,QAAQ,EAAEyC,IAAI,CAACG,GAAG,CAAC,GAAG,EAAE,CAAC,GAAIlE,UAAU,GAAG,GAAI,CAAC;YAAE;YACjDmE,MAAM,EAAEC,QAAQ;YAChBP,WAAW,EAAE;cAAEvC,QAAQ,EAAE;YAAI,CAAC;YAC9BwC,SAAS,EAAE;cAAExC,QAAQ,EAAE;YAAI;UAC7B;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGbpD,OAAA,CAACN,MAAM,CAAC2D,GAAG;QACTN,SAAS,EAAC,uBAAuB;QACjCmC,QAAQ,EAAEvD,iBAAkB;QAC5BkC,OAAO,EAAC,QAAQ;QAChBP,OAAO,EAAC,SAAS;QAAAN,QAAA,gBAEjBhD,OAAA,CAACN,MAAM,CAACyF,EAAE;UACRpC,SAAS,EAAC,qCAAqC;UAC/CsB,KAAK,EAAE;YACL5B,KAAK,EAAEL,WAAW,CAAC/B,KAAK,CAAC,CAACoC;UAC5B,CAAE;UACFa,OAAO,EAAE;YACPC,KAAK,EAAE,CAAC,GAAI1C,UAAU,GAAG,GAAI,IAAIK,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;YACzDkE,UAAU,EAAE,OAAO,EAAE,GAAIvE,UAAU,GAAG,EAAG,wBAAwB,EAAE,GAAIA,UAAU,GAAG,EAAG;UACzF,CAAE;UACFqE,QAAQ,EAAEjD,YAAa;UACvBF,UAAU,EAAE;YACVwB,KAAK,EAAE;cAAEE,IAAI,EAAE,QAAQ;cAAEC,OAAO,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAI,CAAC;YACtDyB,UAAU,EAAE;cAAEjD,QAAQ,EAAE;YAAI;UAC9B,CAAE;UAAAa,QAAA,EAEDZ,WAAW,CAAC/B,KAAK,CAAC,CAACkC;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEZpD,OAAA,CAACN,MAAM,CAAC2F,CAAC;UACPtC,SAAS,EAAC,kCAAkC;UAC5CmC,QAAQ,EAAEjD,YAAa;UACvBqB,OAAO,EAAE;YACPzB,OAAO,EAAE,GAAG,GAAId,QAAQ,GAAG,GAAI;YAC/BwC,KAAK,EAAE,CAAC,GAAIvC,WAAW,GAAG;UAC5B,CAAE;UACFe,UAAU,EAAE;YACVF,OAAO,EAAE;cAAEM,QAAQ,EAAE;YAAI,CAAC;YAC1BoB,KAAK,EAAE;cAAEpB,QAAQ,EAAE;YAAI;UACzB,CAAE;UAAAa,QAAA,EAEDZ,WAAW,CAAC/B,KAAK,CAAC,CAACmC;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGbpD,OAAA,CAACN,MAAM,CAAC2D,GAAG;QACTN,SAAS,EAAC,OAAO;QACjBmC,QAAQ,EAAEjD,YAAa;QACvB4B,OAAO,EAAC,QAAQ;QAChBP,OAAO,EAAC,SAAS;QAAAN,QAAA,eAEjBhD,OAAA;UAAO+C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAC1BhD,OAAA;YACEyD,IAAI,EAAC,UAAU;YACf/B,OAAO,EAAEnB,SAAU;YACnB+E,QAAQ,EAAGC,CAAC,IAAK9D,eAAe,CAAC8D,CAAC,CAACC,MAAM,CAAC9D,OAAO;UAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFpD,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBhD,OAAA;cAAK+C,SAAS,EAAC,SAAS;cAAC0C,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,SAAS;cAACrB,KAAK,EAAE;gBAAE5B,KAAK,EAAE;cAAU,CAAE;cAAAO,QAAA,gBACtFhD,OAAA;gBAAM2F,CAAC,EAAC;cAA8E;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACxFpD,OAAA;gBAAM2F,CAAC,EAAC;cAAsG;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAEhHpD,OAAA;gBACE4F,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBACbC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBACfC,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAC,KAAK;gBACjBC,aAAa,EAAC;cAAO;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpD,OAAA;cAAM+C,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGxCpD,OAAA;cAAK+C,SAAS,EAAC,UAAU;cAAC0C,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAA1C,QAAA,gBAC/DhD,OAAA;gBAAM2F,CAAC,EAAC;cAA8E;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACxFpD,OAAA;gBAAM2F,CAAC,EAAC;cAAsG;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAEhHpD,OAAA;gBAAQmG,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAACxE,OAAO,EAAC,KAAK;gBAAAmB,QAAA,gBACxChD,OAAA;kBAASsG,aAAa,EAAC,GAAG;kBAACC,MAAM,EAAC,OAAO;kBAACC,GAAG,EAAC,IAAI;kBAACC,WAAW,EAAC;gBAAY;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC7EpD,OAAA;kBAASsG,aAAa,EAAC,SAAS;kBAACC,MAAM,EAAC,aAAa;kBAACC,GAAG,EAAC,IAAI;kBAACC,WAAW,EAAC;gBAAY;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACTpD,OAAA;gBAAQmG,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAACxE,OAAO,EAAC,KAAK;gBAAAmB,QAAA,gBACxChD,OAAA;kBAASsG,aAAa,EAAC,GAAG;kBAACC,MAAM,EAAC,OAAO;kBAACC,GAAG,EAAC,MAAM;kBAACC,WAAW,EAAC;gBAAY;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/EpD,OAAA;kBAASsG,aAAa,EAAC,SAAS;kBAACC,MAAM,EAAC,aAAa;kBAACC,GAAG,EAAC,MAAM;kBAACC,WAAW,EAAC;gBAAY;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpD,OAAA;cAAM+C,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGbpD,OAAA,CAACN,MAAM,CAAC2D,GAAG;QACTN,SAAS,EAAC,qBAAqB;QAC/BmC,QAAQ,EAAEjD,YAAa;QACvB4B,OAAO,EAAC,QAAQ;QAChBP,OAAO,EAAC,SAAS;QAAAN,QAAA,eAEjBhD,OAAA;UAAK+C,SAAS,EAAE,gBAAgBpC,cAAc,GAAG,SAAS,GAAG,EAAE,EAAG;UAAAqC,QAAA,gBAChEhD,OAAA;YACEyD,IAAI,EAAC,MAAM;YACXV,SAAS,EAAC,OAAO;YACjB2D,WAAW,EAAC,kDAAkD;YAC9DC,KAAK,EAAElG,SAAU;YACjB6E,QAAQ,EAAGC,CAAC,IAAK7E,YAAY,CAAC6E,CAAC,CAACC,MAAM,CAACmB,KAAK,CAAE;YAC9CC,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAC,IAAI,CAAE;YACvCiG,MAAM,EAAEA,CAAA,KAAMjG,iBAAiB,CAAC,KAAK,CAAE;YACvCkG,SAAS,EAAGvB,CAAC,IAAK;cAChB,IAAIA,CAAC,CAACwB,GAAG,KAAK,OAAO,IAAItG,SAAS,CAACuG,IAAI,CAAC,CAAC,EAAE;gBACzC;gBACA,MAAMC,OAAO,GAAGxG,SAAS,CAACuG,IAAI,CAAC,CAAC;gBAChCtG,YAAY,CAAC,EAAE,CAAC;gBAChBP,WAAW,CAAC8G,OAAO,CAAC;cACtB;YACF;UAAE;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFpD,OAAA;YAAK+C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNpD,OAAA;MAAK+C,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvChD,OAAA,CAACN,MAAM,CAAC2D,GAAG;QACTN,SAAS,EAAC,MAAM;QAChBmC,QAAQ,EAAEvD,iBAAkB;QAC5BkC,OAAO,EAAC,QAAQ;QAChBP,OAAO,EAAC,SAAS;QAAAN,QAAA,GAEhBkE,MAAM,CAACC,IAAI,CAAC/E,WAAW,CAAC,CAACgF,GAAG,CAAEC,CAAC,iBAC9BrH,OAAA,CAACN,MAAM,CAAC4H,MAAM;UAEZvE,SAAS,EAAE,QAAQ1C,KAAK,KAAKgH,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjDhD,KAAK,EAAE;YACLkD,eAAe,EAAElH,KAAK,KAAKgH,CAAC,GAAGjF,WAAW,CAACiF,CAAC,CAAC,CAAC5E,KAAK,GAAG,IAAI,GAAG,aAAa;YAC1E+E,MAAM,EAAEnH,KAAK,KAAKgH,CAAC,GAAG,aAAajF,WAAW,CAACiF,CAAC,CAAC,CAAC5E,KAAK,IAAI,GAAG;UAChE,CAAE;UACFgF,UAAU,EAAE;YACVlE,KAAK,EAAE,IAAI;YACXoB,SAAS,EAAE,YAAYvC,WAAW,CAACiF,CAAC,CAAC,CAAC5E,KAAK;UAC7C,CAAE;UACFiF,QAAQ,EAAE;YAAEnE,KAAK,EAAE;UAAK,CAAE;UAC1BoE,OAAO,EAAEA,CAAA,KAAMrH,QAAQ,CAAC+G,CAAC,CAAE;UAC3BnC,QAAQ,EAAEjD,YAAa;UACvBM,KAAK,EAAE8E,CAAC,CAACO,WAAW,CAAC,CAAE;UAAA5E,QAAA,gBAEvBhD,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBhD,OAAA;cACE+C,SAAS,EAAC,yEAAyE;cACnFsB,KAAK,EAAE;gBACLkD,eAAe,EAAElH,KAAK,KAAKgH,CAAC,GAAGjF,WAAW,CAACiF,CAAC,CAAC,CAAC5E,KAAK,GAAG,wBAAwB;gBAC9EA,KAAK,EAAE,OAAO;gBACdoF,QAAQ,EAAE;cACZ,CAAE;cAAA7E,QAAA,EAEDqE,CAAC,CAACS,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC;YAAC;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpD,OAAA;YAAM+C,SAAS,EAAC,YAAY;YAAAC,QAAA,EACzBqE,CAAC,CAACS,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,GAAGP,CAAC,CAACU,KAAK,CAAC,CAAC;UAAC;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA,GA7BFiE,CAAC;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BO,CAChB,CAAC,EAGDlD,YAAY,iBACXF,OAAA,CAACN,MAAM,CAAC4H,MAAM;UACZvE,SAAS,EAAC,MAAM;UAChBsB,KAAK,EAAE;YACLkD,eAAe,EAAE,aAAa;YAC9BC,MAAM,EAAE;UACV,CAAE;UACFC,UAAU,EAAE;YACVlE,KAAK,EAAE,IAAI;YACXoB,SAAS,EAAE;UACb,CAAE;UACF+C,QAAQ,EAAE;YAAEnE,KAAK,EAAE;UAAK,CAAE;UAC1BoE,OAAO,EAAEzH,YAAa;UACtBgF,QAAQ,EAAEjD,YAAa;UACvBM,KAAK,EAAC,MAAM;UAAAS,QAAA,gBAEZhD,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBhD,OAAA;cACE+C,SAAS,EAAC,yEAAyE;cACnFsB,KAAK,EAAE;gBACLkD,eAAe,EAAE,0BAA0B;gBAC3C9E,KAAK,EAAE,OAAO;gBACdoF,QAAQ,EAAE;cACZ,CAAE;cAAA7E,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpD,OAAA;YAAM+C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAAChD,EAAA,CAhYIH,MAAM;EAAA,QAeNH,qBAAqB;AAAA;AAAAkI,EAAA,GAfrB/H,MAAM;AAkYZ,eAAeA,MAAM;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}