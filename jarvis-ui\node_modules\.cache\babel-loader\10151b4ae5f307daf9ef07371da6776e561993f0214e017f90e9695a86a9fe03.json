{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\UI Designolddd\\\\jarvis-ui\\\\src\\\\components\\\\AudioBackendTester.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport usePythonAudioBackend from '../hooks/usePythonAudioBackend';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AudioBackendTester = () => {\n  _s();\n  const [usePythonBackend, setUsePythonBackend] = useState(false);\n  const audioData = usePythonAudioBackend({\n    enabled: true,\n    usePythonBackend: usePythonBackend,\n    serverUrl: 'ws://localhost:8765',\n    // C# backend\n    pythonServerUrl: 'ws://localhost:8766' // Python backend\n  });\n  const {\n    // Basic audio data\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    beatDetected,\n    isAudioActive,\n    audioSource,\n    isSystemAudio,\n    // Advanced frequency bands\n    subBassLevel,\n    lowMidrangeLevel,\n    upperMidrangeLevel,\n    presenceLevel,\n    brillianceLevel,\n    // Advanced beat detection\n    subBassBeat,\n    bassBeat,\n    lowMidrangeBeat,\n    midrangeBeat,\n    upperMidrangeBeat,\n    presenceBeat,\n    brillianceBeat,\n    // Connection state\n    isConnected,\n    connectionError,\n    reconnectAttempts,\n    // Connection controls\n    connect,\n    disconnect\n  } = audioData;\n  const handleBackendSwitch = pythonMode => {\n    disconnect(); // Disconnect current backend\n    setUsePythonBackend(pythonMode);\n    setTimeout(() => {\n      connect(); // Connect to new backend after state update\n    }, 500);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '20px',\n      right: '20px',\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      fontFamily: 'monospace',\n      fontSize: '12px',\n      zIndex: 1000,\n      minWidth: '300px',\n      maxHeight: '80vh',\n      overflowY: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        margin: '0 0 15px 0',\n        color: '#00ff00'\n      },\n      children: \"\\uD83C\\uDFB5 Audio Backend Tester\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px',\n          fontWeight: 'bold'\n        },\n        children: \"Backend Selection:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handleBackendSwitch(false),\n        style: {\n          background: !usePythonBackend ? '#00ff00' : '#333',\n          color: !usePythonBackend ? 'black' : 'white',\n          border: 'none',\n          padding: '5px 10px',\n          marginRight: '10px',\n          borderRadius: '5px',\n          cursor: 'pointer'\n        },\n        children: \"C# WASAPI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handleBackendSwitch(true),\n        style: {\n          background: usePythonBackend ? '#00ff00' : '#333',\n          color: usePythonBackend ? 'black' : 'white',\n          border: 'none',\n          padding: '5px 10px',\n          borderRadius: '5px',\n          cursor: 'pointer'\n        },\n        children: \"Python FFT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          marginBottom: '5px'\n        },\n        children: \"Connection Status:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: isConnected ? '#00ff00' : '#ff0000'\n        },\n        children: isConnected ? '✅ Connected' : '❌ Disconnected'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), connectionError && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#ff0000',\n          fontSize: '10px'\n        },\n        children: [\"Error: \", connectionError]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#888'\n        },\n        children: [\"Backend: \", usePythonBackend ? 'Python FFT (8766)' : 'C# WASAPI (8765)']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#888'\n        },\n        children: [\"Source: \", audioSource]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          marginBottom: '5px'\n        },\n        children: \"Basic Audio Levels:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Audio: \", audioLevel.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Bass: \", bassLevel.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Mid: \", midLevel.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Treble: \", trebleLevel.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: beatDetected ? '#ff0000' : '#666'\n        },\n        children: [\"Beat: \", beatDetected ? '🔥 DETECTED' : '⚪ None']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          marginBottom: '5px'\n        },\n        children: \"Advanced Frequency Bands:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Sub Bass (20-60Hz): \", (subBassLevel === null || subBassLevel === void 0 ? void 0 : subBassLevel.toFixed(1)) || 0, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Low Mid (250-500Hz): \", (lowMidrangeLevel === null || lowMidrangeLevel === void 0 ? void 0 : lowMidrangeLevel.toFixed(1)) || 0, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Upper Mid (2-4kHz): \", (upperMidrangeLevel === null || upperMidrangeLevel === void 0 ? void 0 : upperMidrangeLevel.toFixed(1)) || 0, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Presence (4-6kHz): \", (presenceLevel === null || presenceLevel === void 0 ? void 0 : presenceLevel.toFixed(1)) || 0, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Brilliance (6-20kHz): \", (brillianceLevel === null || brillianceLevel === void 0 ? void 0 : brillianceLevel.toFixed(1)) || 0, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          marginBottom: '5px'\n        },\n        children: \"Advanced Beat Detection:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: subBassBeat ? '#ff0000' : '#666'\n        },\n        children: [\"Sub Bass: \", subBassBeat ? '🔊' : '⚪']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: bassBeat ? '#ff0000' : '#666'\n        },\n        children: [\"Bass: \", bassBeat ? '🥁' : '⚪']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: lowMidrangeBeat ? '#ff0000' : '#666'\n        },\n        children: [\"Low Mid: \", lowMidrangeBeat ? '🎸' : '⚪']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: midrangeBeat ? '#ff0000' : '#666'\n        },\n        children: [\"Midrange: \", midrangeBeat ? '🎤' : '⚪']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: upperMidrangeBeat ? '#ff0000' : '#666'\n        },\n        children: [\"Upper Mid: \", upperMidrangeBeat ? '🎺' : '⚪']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: presenceBeat ? '#ff0000' : '#666'\n        },\n        children: [\"Presence: \", presenceBeat ? '✨' : '⚪']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: brillianceBeat ? '#ff0000' : '#666'\n        },\n        children: [\"Brilliance: \", brillianceBeat ? '💎' : '⚪']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: connect,\n        disabled: isConnected,\n        style: {\n          background: '#00ff00',\n          color: 'black',\n          border: 'none',\n          padding: '5px 10px',\n          marginRight: '10px',\n          borderRadius: '5px',\n          cursor: isConnected ? 'not-allowed' : 'pointer',\n          opacity: isConnected ? 0.5 : 1\n        },\n        children: \"Connect\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: disconnect,\n        disabled: !isConnected,\n        style: {\n          background: '#ff0000',\n          color: 'white',\n          border: 'none',\n          padding: '5px 10px',\n          borderRadius: '5px',\n          cursor: !isConnected ? 'not-allowed' : 'pointer',\n          opacity: !isConnected ? 0.5 : 1\n        },\n        children: \"Disconnect\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(AudioBackendTester, \"wuQf2AAvtg5vRzUz+gs/08GOsCg=\", false, function () {\n  return [usePythonAudioBackend];\n});\n_c = AudioBackendTester;\nexport default AudioBackendTester;\nvar _c;\n$RefreshReg$(_c, \"AudioBackendTester\");", "map": {"version": 3, "names": ["React", "useState", "usePythonAudioBackend", "jsxDEV", "_jsxDEV", "AudioBackendTester", "_s", "usePythonBackend", "setUsePythonBackend", "audioData", "enabled", "serverUrl", "pythonServerUrl", "audioLevel", "bassLevel", "midLevel", "trebleLevel", "beatDetected", "isAudioActive", "audioSource", "isSystemAudio", "subBassLevel", "lowMidrangeLevel", "upperMidrangeLevel", "presenceLevel", "brillianceLevel", "subBassBeat", "bassBeat", "lowMidrangeBeat", "midrangeBeat", "upperMidrangeBeat", "presenceBeat", "brillianceBeat", "isConnected", "connectionError", "reconnectAttempts", "connect", "disconnect", "handleBackendSwitch", "pythonMode", "setTimeout", "style", "position", "top", "right", "background", "color", "padding", "borderRadius", "fontFamily", "fontSize", "zIndex", "min<PERSON><PERSON><PERSON>", "maxHeight", "overflowY", "children", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "fontWeight", "onClick", "border", "marginRight", "cursor", "toFixed", "disabled", "opacity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/UI Designolddd/jarvis-ui/src/components/AudioBackendTester.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport usePythonAudioBackend from '../hooks/usePythonAudioBackend';\n\nconst AudioBackendTester = () => {\n  const [usePythonBackend, setUsePythonBackend] = useState(false);\n  \n  const audioData = usePythonAudioBackend({\n    enabled: true,\n    usePythonBackend: usePythonBackend,\n    serverUrl: 'ws://localhost:8765', // C# backend\n    pythonServerUrl: 'ws://localhost:8766', // Python backend\n  });\n\n  const {\n    // Basic audio data\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    beatDetected,\n    isAudioActive,\n    audioSource,\n    isSystemAudio,\n\n    // Advanced frequency bands\n    subBassLevel,\n    lowMidrangeLevel,\n    upperMidrangeLevel,\n    presenceLevel,\n    brillianceLevel,\n\n    // Advanced beat detection\n    subBassBeat,\n    bassBeat,\n    lowMidrangeBeat,\n    midrangeBeat,\n    upperMidrangeBeat,\n    presenceBeat,\n    brillianceBeat,\n\n    // Connection state\n    isConnected,\n    connectionError,\n    reconnectAttempts,\n\n    // Connection controls\n    connect,\n    disconnect,\n  } = audioData;\n\n  const handleBackendSwitch = (pythonMode) => {\n    disconnect(); // Disconnect current backend\n    setUsePythonBackend(pythonMode);\n    setTimeout(() => {\n      connect(); // Connect to new backend after state update\n    }, 500);\n  };\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '20px',\n      right: '20px',\n      background: 'rgba(0, 0, 0, 0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      fontFamily: 'monospace',\n      fontSize: '12px',\n      zIndex: 1000,\n      minWidth: '300px',\n      maxHeight: '80vh',\n      overflowY: 'auto'\n    }}>\n      <h3 style={{ margin: '0 0 15px 0', color: '#00ff00' }}>\n        🎵 Audio Backend Tester\n      </h3>\n      \n      {/* Backend Selection */}\n      <div style={{ marginBottom: '15px' }}>\n        <div style={{ marginBottom: '10px', fontWeight: 'bold' }}>Backend Selection:</div>\n        <button\n          onClick={() => handleBackendSwitch(false)}\n          style={{\n            background: !usePythonBackend ? '#00ff00' : '#333',\n            color: !usePythonBackend ? 'black' : 'white',\n            border: 'none',\n            padding: '5px 10px',\n            marginRight: '10px',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          }}\n        >\n          C# WASAPI\n        </button>\n        <button\n          onClick={() => handleBackendSwitch(true)}\n          style={{\n            background: usePythonBackend ? '#00ff00' : '#333',\n            color: usePythonBackend ? 'black' : 'white',\n            border: 'none',\n            padding: '5px 10px',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          }}\n        >\n          Python FFT\n        </button>\n      </div>\n\n      {/* Connection Status */}\n      <div style={{ marginBottom: '15px' }}>\n        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Connection Status:</div>\n        <div style={{ color: isConnected ? '#00ff00' : '#ff0000' }}>\n          {isConnected ? '✅ Connected' : '❌ Disconnected'}\n        </div>\n        {connectionError && (\n          <div style={{ color: '#ff0000', fontSize: '10px' }}>\n            Error: {connectionError}\n          </div>\n        )}\n        <div style={{ fontSize: '10px', color: '#888' }}>\n          Backend: {usePythonBackend ? 'Python FFT (8766)' : 'C# WASAPI (8765)'}\n        </div>\n        <div style={{ fontSize: '10px', color: '#888' }}>\n          Source: {audioSource}\n        </div>\n      </div>\n\n      {/* Basic Audio Levels */}\n      <div style={{ marginBottom: '15px' }}>\n        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Basic Audio Levels:</div>\n        <div>Audio: {audioLevel.toFixed(1)}%</div>\n        <div>Bass: {bassLevel.toFixed(1)}%</div>\n        <div>Mid: {midLevel.toFixed(1)}%</div>\n        <div>Treble: {trebleLevel.toFixed(1)}%</div>\n        <div style={{ color: beatDetected ? '#ff0000' : '#666' }}>\n          Beat: {beatDetected ? '🔥 DETECTED' : '⚪ None'}\n        </div>\n      </div>\n\n      {/* Advanced Frequency Bands */}\n      <div style={{ marginBottom: '15px' }}>\n        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Advanced Frequency Bands:</div>\n        <div>Sub Bass (20-60Hz): {subBassLevel?.toFixed(1) || 0}%</div>\n        <div>Low Mid (250-500Hz): {lowMidrangeLevel?.toFixed(1) || 0}%</div>\n        <div>Upper Mid (2-4kHz): {upperMidrangeLevel?.toFixed(1) || 0}%</div>\n        <div>Presence (4-6kHz): {presenceLevel?.toFixed(1) || 0}%</div>\n        <div>Brilliance (6-20kHz): {brillianceLevel?.toFixed(1) || 0}%</div>\n      </div>\n\n      {/* Advanced Beat Detection */}\n      <div style={{ marginBottom: '15px' }}>\n        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Advanced Beat Detection:</div>\n        <div style={{ color: subBassBeat ? '#ff0000' : '#666' }}>\n          Sub Bass: {subBassBeat ? '🔊' : '⚪'}\n        </div>\n        <div style={{ color: bassBeat ? '#ff0000' : '#666' }}>\n          Bass: {bassBeat ? '🥁' : '⚪'}\n        </div>\n        <div style={{ color: lowMidrangeBeat ? '#ff0000' : '#666' }}>\n          Low Mid: {lowMidrangeBeat ? '🎸' : '⚪'}\n        </div>\n        <div style={{ color: midrangeBeat ? '#ff0000' : '#666' }}>\n          Midrange: {midrangeBeat ? '🎤' : '⚪'}\n        </div>\n        <div style={{ color: upperMidrangeBeat ? '#ff0000' : '#666' }}>\n          Upper Mid: {upperMidrangeBeat ? '🎺' : '⚪'}\n        </div>\n        <div style={{ color: presenceBeat ? '#ff0000' : '#666' }}>\n          Presence: {presenceBeat ? '✨' : '⚪'}\n        </div>\n        <div style={{ color: brillianceBeat ? '#ff0000' : '#666' }}>\n          Brilliance: {brillianceBeat ? '💎' : '⚪'}\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div>\n        <button\n          onClick={connect}\n          disabled={isConnected}\n          style={{\n            background: '#00ff00',\n            color: 'black',\n            border: 'none',\n            padding: '5px 10px',\n            marginRight: '10px',\n            borderRadius: '5px',\n            cursor: isConnected ? 'not-allowed' : 'pointer',\n            opacity: isConnected ? 0.5 : 1\n          }}\n        >\n          Connect\n        </button>\n        <button\n          onClick={disconnect}\n          disabled={!isConnected}\n          style={{\n            background: '#ff0000',\n            color: 'white',\n            border: 'none',\n            padding: '5px 10px',\n            borderRadius: '5px',\n            cursor: !isConnected ? 'not-allowed' : 'pointer',\n            opacity: !isConnected ? 0.5 : 1\n          }}\n        >\n          Disconnect\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default AudioBackendTester;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,qBAAqB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMQ,SAAS,GAAGP,qBAAqB,CAAC;IACtCQ,OAAO,EAAE,IAAI;IACbH,gBAAgB,EAAEA,gBAAgB;IAClCI,SAAS,EAAE,qBAAqB;IAAE;IAClCC,eAAe,EAAE,qBAAqB,CAAE;EAC1C,CAAC,CAAC;EAEF,MAAM;IACJ;IACAC,UAAU;IACVC,SAAS;IACTC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC,aAAa;IACbC,WAAW;IACXC,aAAa;IAEb;IACAC,YAAY;IACZC,gBAAgB;IAChBC,kBAAkB;IAClBC,aAAa;IACbC,eAAe;IAEf;IACAC,WAAW;IACXC,QAAQ;IACRC,eAAe;IACfC,YAAY;IACZC,iBAAiB;IACjBC,YAAY;IACZC,cAAc;IAEd;IACAC,WAAW;IACXC,eAAe;IACfC,iBAAiB;IAEjB;IACAC,OAAO;IACPC;EACF,CAAC,GAAG5B,SAAS;EAEb,MAAM6B,mBAAmB,GAAIC,UAAU,IAAK;IAC1CF,UAAU,CAAC,CAAC,CAAC,CAAC;IACd7B,mBAAmB,CAAC+B,UAAU,CAAC;IAC/BC,UAAU,CAAC,MAAM;MACfJ,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,oBACEhC,OAAA;IAAKqC,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE,oBAAoB;MAChCC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,WAAW;MACvBC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,gBACAnD,OAAA;MAAIqC,KAAK,EAAE;QAAEe,MAAM,EAAE,YAAY;QAAEV,KAAK,EAAE;MAAU,CAAE;MAAAS,QAAA,EAAC;IAEvD;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLxD,OAAA;MAAKqC,KAAK,EAAE;QAAEoB,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,gBACnCnD,OAAA;QAAKqC,KAAK,EAAE;UAAEoB,YAAY,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAP,QAAA,EAAC;MAAkB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClFxD,OAAA;QACE2D,OAAO,EAAEA,CAAA,KAAMzB,mBAAmB,CAAC,KAAK,CAAE;QAC1CG,KAAK,EAAE;UACLI,UAAU,EAAE,CAACtC,gBAAgB,GAAG,SAAS,GAAG,MAAM;UAClDuC,KAAK,EAAE,CAACvC,gBAAgB,GAAG,OAAO,GAAG,OAAO;UAC5CyD,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,UAAU;UACnBkB,WAAW,EAAE,MAAM;UACnBjB,YAAY,EAAE,KAAK;UACnBkB,MAAM,EAAE;QACV,CAAE;QAAAX,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA;QACE2D,OAAO,EAAEA,CAAA,KAAMzB,mBAAmB,CAAC,IAAI,CAAE;QACzCG,KAAK,EAAE;UACLI,UAAU,EAAEtC,gBAAgB,GAAG,SAAS,GAAG,MAAM;UACjDuC,KAAK,EAAEvC,gBAAgB,GAAG,OAAO,GAAG,OAAO;UAC3CyD,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,UAAU;UACnBC,YAAY,EAAE,KAAK;UACnBkB,MAAM,EAAE;QACV,CAAE;QAAAX,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNxD,OAAA;MAAKqC,KAAK,EAAE;QAAEoB,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,gBACnCnD,OAAA;QAAKqC,KAAK,EAAE;UAAEqB,UAAU,EAAE,MAAM;UAAED,YAAY,EAAE;QAAM,CAAE;QAAAN,QAAA,EAAC;MAAkB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjFxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAEb,WAAW,GAAG,SAAS,GAAG;QAAU,CAAE;QAAAsB,QAAA,EACxDtB,WAAW,GAAG,aAAa,GAAG;MAAgB;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,EACL1B,eAAe,iBACd9B,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEI,QAAQ,EAAE;QAAO,CAAE;QAAAK,QAAA,GAAC,SAC3C,EAACrB,eAAe;MAAA;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACN,eACDxD,OAAA;QAAKqC,KAAK,EAAE;UAAES,QAAQ,EAAE,MAAM;UAAEJ,KAAK,EAAE;QAAO,CAAE;QAAAS,QAAA,GAAC,WACtC,EAAChD,gBAAgB,GAAG,mBAAmB,GAAG,kBAAkB;MAAA;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNxD,OAAA;QAAKqC,KAAK,EAAE;UAAES,QAAQ,EAAE,MAAM;UAAEJ,KAAK,EAAE;QAAO,CAAE;QAAAS,QAAA,GAAC,UACvC,EAACpC,WAAW;MAAA;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAKqC,KAAK,EAAE;QAAEoB,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,gBACnCnD,OAAA;QAAKqC,KAAK,EAAE;UAAEqB,UAAU,EAAE,MAAM;UAAED,YAAY,EAAE;QAAM,CAAE;QAAAN,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClFxD,OAAA;QAAAmD,QAAA,GAAK,SAAO,EAAC1C,UAAU,CAACsD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CxD,OAAA;QAAAmD,QAAA,GAAK,QAAM,EAACzC,SAAS,CAACqD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxCxD,OAAA;QAAAmD,QAAA,GAAK,OAAK,EAACxC,QAAQ,CAACoD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtCxD,OAAA;QAAAmD,QAAA,GAAK,UAAQ,EAACvC,WAAW,CAACmD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5CxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAE7B,YAAY,GAAG,SAAS,GAAG;QAAO,CAAE;QAAAsC,QAAA,GAAC,QAClD,EAACtC,YAAY,GAAG,aAAa,GAAG,QAAQ;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAKqC,KAAK,EAAE;QAAEoB,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,gBACnCnD,OAAA;QAAKqC,KAAK,EAAE;UAAEqB,UAAU,EAAE,MAAM;UAAED,YAAY,EAAE;QAAM,CAAE;QAAAN,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxFxD,OAAA;QAAAmD,QAAA,GAAK,sBAAoB,EAAC,CAAAlC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/DxD,OAAA;QAAAmD,QAAA,GAAK,uBAAqB,EAAC,CAAAjC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpExD,OAAA;QAAAmD,QAAA,GAAK,sBAAoB,EAAC,CAAAhC,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAE4C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrExD,OAAA;QAAAmD,QAAA,GAAK,qBAAmB,EAAC,CAAA/B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/DxD,OAAA;QAAAmD,QAAA,GAAK,wBAAsB,EAAC,CAAA9B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0C,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAGNxD,OAAA;MAAKqC,KAAK,EAAE;QAAEoB,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,gBACnCnD,OAAA;QAAKqC,KAAK,EAAE;UAAEqB,UAAU,EAAE,MAAM;UAAED,YAAY,EAAE;QAAM,CAAE;QAAAN,QAAA,EAAC;MAAwB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvFxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAEpB,WAAW,GAAG,SAAS,GAAG;QAAO,CAAE;QAAA6B,QAAA,GAAC,YAC7C,EAAC7B,WAAW,GAAG,IAAI,GAAG,GAAG;MAAA;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACNxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAEnB,QAAQ,GAAG,SAAS,GAAG;QAAO,CAAE;QAAA4B,QAAA,GAAC,QAC9C,EAAC5B,QAAQ,GAAG,IAAI,GAAG,GAAG;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACNxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAElB,eAAe,GAAG,SAAS,GAAG;QAAO,CAAE;QAAA2B,QAAA,GAAC,WAClD,EAAC3B,eAAe,GAAG,IAAI,GAAG,GAAG;MAAA;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACNxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAEjB,YAAY,GAAG,SAAS,GAAG;QAAO,CAAE;QAAA0B,QAAA,GAAC,YAC9C,EAAC1B,YAAY,GAAG,IAAI,GAAG,GAAG;MAAA;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAEhB,iBAAiB,GAAG,SAAS,GAAG;QAAO,CAAE;QAAAyB,QAAA,GAAC,aAClD,EAACzB,iBAAiB,GAAG,IAAI,GAAG,GAAG;MAAA;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACNxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAEf,YAAY,GAAG,SAAS,GAAG;QAAO,CAAE;QAAAwB,QAAA,GAAC,YAC9C,EAACxB,YAAY,GAAG,GAAG,GAAG,GAAG;MAAA;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACNxD,OAAA;QAAKqC,KAAK,EAAE;UAAEK,KAAK,EAAEd,cAAc,GAAG,SAAS,GAAG;QAAO,CAAE;QAAAuB,QAAA,GAAC,cAC9C,EAACvB,cAAc,GAAG,IAAI,GAAG,GAAG;MAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAAmD,QAAA,gBACEnD,OAAA;QACE2D,OAAO,EAAE3B,OAAQ;QACjBgC,QAAQ,EAAEnC,WAAY;QACtBQ,KAAK,EAAE;UACLI,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,OAAO;UACdkB,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,UAAU;UACnBkB,WAAW,EAAE,MAAM;UACnBjB,YAAY,EAAE,KAAK;UACnBkB,MAAM,EAAEjC,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CoC,OAAO,EAAEpC,WAAW,GAAG,GAAG,GAAG;QAC/B,CAAE;QAAAsB,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA;QACE2D,OAAO,EAAE1B,UAAW;QACpB+B,QAAQ,EAAE,CAACnC,WAAY;QACvBQ,KAAK,EAAE;UACLI,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,OAAO;UACdkB,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,UAAU;UACnBC,YAAY,EAAE,KAAK;UACnBkB,MAAM,EAAE,CAACjC,WAAW,GAAG,aAAa,GAAG,SAAS;UAChDoC,OAAO,EAAE,CAACpC,WAAW,GAAG,GAAG,GAAG;QAChC,CAAE;QAAAsB,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAlNID,kBAAkB;EAAA,QAGJH,qBAAqB;AAAA;AAAAoE,EAAA,GAHnCjE,kBAAkB;AAoNxB,eAAeA,kBAAkB;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}