import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import <PERSON> from './components/Jarvis';
import HomeScreen from './components/HomeScreen';
import ChatScreen from './components/ChatScreen';

function App() {
  const [currentScreen, setCurrentScreen] = useState('home'); // 'home', 'jarvis', 'chat'
  const [chatInitialMessage, setChatInitialMessage] = useState('');

  const handleEnterJarvis = () => {
    setCurrentScreen('jarvis');
  };

  const handleExitToHome = () => {
    setCurrentScreen('home');
  };

  const handleEnterChat = (initialMessage = '') => {
    setChatInitialMessage(initialMessage);
    setCurrentScreen('chat');
  };

  const handleExitToJarvis = () => {
    setCurrentScreen('jarvis');
    setChatInitialMessage(''); // Clear the initial message
  };

  // Animation variants
  const slideVariants = {
    initial: {
      y: '100%',
      opacity: 0
    },
    animate: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 120,
        duration: 0.8
      }
    },
    exit: {
      y: '-100%',
      opacity: 0,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 120,
        duration: 0.6
      }
    }
  };

  const fadeVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: { duration: 0.5 }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <div className="App bg-gradient-to-br from-gray-900 to-black min-h-screen text-white relative overflow-hidden">

      <AnimatePresence mode="wait">
        {currentScreen === 'home' && (
          <motion.div
            key="home"
            variants={fadeVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="absolute inset-0"
          >
            <HomeScreen onEnterJarvis={handleEnterJarvis} />
          </motion.div>
        )}

        {currentScreen === 'jarvis' && (
          <motion.div
            key="jarvis"
            variants={fadeVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="absolute inset-0"
          >
            <Jarvis onExitToHome={handleExitToHome} onEnterChat={handleEnterChat} />
          </motion.div>
        )}

        {currentScreen === 'chat' && (
          <motion.div
            key="chat"
            variants={slideVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="absolute inset-0"
          >
            <ChatScreen onExitToHome={handleExitToJarvis} initialMessage={chatInitialMessage} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default App;