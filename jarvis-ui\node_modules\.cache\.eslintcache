[{"C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js": "3", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js": "4", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js": "5", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js": "6", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js": "7", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js": "8", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js": "9", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js": "10", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js": "11", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js": "12", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js": "13", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js": "14", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\SystemAudioStatus.js": "15", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\usePythonAudioBackend.js": "16", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\AudioBackendTester.js": "17"}, {"size": 254, "mtime": 1750762253811, "results": "18", "hashOfConfig": "19"}, {"size": 2839, "mtime": 1751008229522, "results": "20", "hashOfConfig": "19"}, {"size": 5858, "mtime": 1750966557156, "results": "21", "hashOfConfig": "19"}, {"size": 13944, "mtime": 1751011507180, "results": "22", "hashOfConfig": "19"}, {"size": 910, "mtime": 1750784249896, "results": "23", "hashOfConfig": "19"}, {"size": 1166, "mtime": 1750784262464, "results": "24", "hashOfConfig": "19"}, {"size": 1566, "mtime": 1750784237894, "results": "25", "hashOfConfig": "19"}, {"size": 6839, "mtime": 1750966415822, "results": "26", "hashOfConfig": "19"}, {"size": 1577, "mtime": 1750784209479, "results": "27", "hashOfConfig": "19"}, {"size": 1673, "mtime": 1750784224221, "results": "28", "hashOfConfig": "19"}, {"size": 940, "mtime": 1750785802095, "results": "29", "hashOfConfig": "19"}, {"size": 9562, "mtime": 1750838136053, "results": "30", "hashOfConfig": "19"}, {"size": 13812, "mtime": 1750968668896, "results": "31", "hashOfConfig": "19"}, {"size": 20666, "mtime": 1751006687515, "results": "32", "hashOfConfig": "19"}, {"size": 2991, "mtime": 1751005740875, "results": "33", "hashOfConfig": "19"}, {"size": 10148, "mtime": 1751013500296, "results": "34", "hashOfConfig": "19"}, {"size": 7714, "mtime": 1751013756798, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1edix7f", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js", ["87"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js", ["88", "89", "90", "91"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\SystemAudioStatus.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\usePythonAudioBackend.js", ["92"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\AudioBackendTester.js", ["93", "94", "95"], [], {"ruleId": "96", "severity": 1, "message": "97", "line": 19, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 19, "endColumn": 18}, {"ruleId": "96", "severity": 1, "message": "97", "line": 18, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 18, "endColumn": 18}, {"ruleId": "96", "severity": 1, "message": "100", "line": 20, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 20, "endColumn": 18}, {"ruleId": "96", "severity": 1, "message": "101", "line": 21, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 21, "endColumn": 16}, {"ruleId": "96", "severity": 1, "message": "102", "line": 22, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 22, "endColumn": 16}, {"ruleId": "103", "severity": 1, "message": "104", "line": 156, "column": 6, "nodeType": "105", "endLine": 156, "endColumn": 121, "suggestions": "106"}, {"ruleId": "96", "severity": 1, "message": "97", "line": 24, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 24, "endColumn": 18}, {"ruleId": "96", "severity": 1, "message": "100", "line": 26, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 26, "endColumn": 18}, {"ruleId": "96", "severity": 1, "message": "107", "line": 47, "column": 5, "nodeType": "98", "messageId": "99", "endLine": 47, "endColumn": 22}, "no-unused-vars", "'isAudioActive' is assigned a value but never used.", "Identifier", "unusedVar", "'isSystemAudio' is assigned a value but never used.", "'audioSource' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'remyServerUrl' and 'useRemyBackend'. Either include them or remove the dependency array.", "ArrayExpression", ["108"], "'reconnectAttempts' is assigned a value but never used.", {"desc": "109", "fix": "110"}, "Update the dependencies array to be: [enabled, useRemyBackend, remyServerUrl, usePythonBackend, pythonServerUrl, serverUrl, reconnectAttempts, maxReconnectAttempts, reconnectInterval]", {"range": "111", "text": "112"}, [5988, 6103], "[enabled, useRemyBackend, remyServerUrl, usePythonBackend, pythonServerUrl, serverUrl, reconnectAttempts, maxReconnectAttempts, reconnectInterval]"]