[{"C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js": "3", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js": "4", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js": "5", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js": "6", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js": "7", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js": "8", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js": "9", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js": "10", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js": "11", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js": "12", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js": "13", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js": "14", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\useAudioDetection.js": "15", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AudioTestPanel.js": "16", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\useSystemAudioDetection.js": "17", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\useElectronSystemAudio.js": "18", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\SystemAudioStatus.js": "19", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AudioDebugPanel.js": "20", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\usePythonAudioBackend.js": "21"}, {"size": 254, "mtime": 1750762253811, "results": "22", "hashOfConfig": "23"}, {"size": 3668, "mtime": 1750979416984, "results": "24", "hashOfConfig": "23"}, {"size": 5858, "mtime": 1750966557156, "results": "25", "hashOfConfig": "23"}, {"size": 15292, "mtime": 1751006687178, "results": "26", "hashOfConfig": "23"}, {"size": 910, "mtime": 1750784249896, "results": "27", "hashOfConfig": "23"}, {"size": 1166, "mtime": 1750784262464, "results": "28", "hashOfConfig": "23"}, {"size": 1566, "mtime": 1750784237894, "results": "29", "hashOfConfig": "23"}, {"size": 6839, "mtime": 1750966415822, "results": "30", "hashOfConfig": "23"}, {"size": 1577, "mtime": 1750784209479, "results": "31", "hashOfConfig": "23"}, {"size": 1673, "mtime": 1750784224221, "results": "32", "hashOfConfig": "23"}, {"size": 940, "mtime": 1750785802095, "results": "33", "hashOfConfig": "23"}, {"size": 9562, "mtime": 1750838136053, "results": "34", "hashOfConfig": "23"}, {"size": 13812, "mtime": 1750968668896, "results": "35", "hashOfConfig": "23"}, {"size": 20666, "mtime": 1751006687515, "results": "36", "hashOfConfig": "23"}, {"size": 8389, "mtime": 1750975980612, "results": "37", "hashOfConfig": "23"}, {"size": 11510, "mtime": 1750976219733, "results": "38", "hashOfConfig": "23"}, {"size": 5228, "mtime": 1750976196159, "results": "39", "hashOfConfig": "23"}, {"size": 9071, "mtime": 1751005740861, "results": "40", "hashOfConfig": "23"}, {"size": 2991, "mtime": 1751005740875, "results": "41", "hashOfConfig": "23"}, {"size": 9238, "mtime": 1751006687516, "results": "42", "hashOfConfig": "23"}, {"size": 7026, "mtime": 1750979106126, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1edix7f", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js", ["107"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js", ["108", "109", "110", "111"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\useAudioDetection.js", ["112", "113"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AudioTestPanel.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\useSystemAudioDetection.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\useElectronSystemAudio.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\SystemAudioStatus.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AudioDebugPanel.js", ["114", "115"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\usePythonAudioBackend.js", [], [], {"ruleId": "116", "severity": 1, "message": "117", "line": 37, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 37, "endColumn": 18}, {"ruleId": "116", "severity": 1, "message": "117", "line": 18, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 18, "endColumn": 18}, {"ruleId": "116", "severity": 1, "message": "120", "line": 20, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 20, "endColumn": 18}, {"ruleId": "116", "severity": 1, "message": "121", "line": 21, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 21, "endColumn": 16}, {"ruleId": "116", "severity": 1, "message": "122", "line": 22, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 22, "endColumn": 16}, {"ruleId": "116", "severity": 1, "message": "123", "line": 14, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 14, "endColumn": 14}, {"ruleId": "116", "severity": 1, "message": "124", "line": 17, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 17, "endColumn": 18}, {"ruleId": "116", "severity": 1, "message": "125", "line": 1, "column": 27, "nodeType": "118", "messageId": "119", "endLine": 1, "endColumn": 36}, {"ruleId": "116", "severity": 1, "message": "126", "line": 19, "column": 5, "nodeType": "118", "messageId": "119", "endLine": 19, "endColumn": 12}, "no-unused-vars", "'isAudioActive' is assigned a value but never used.", "Identifier", "unusedVar", "'isSystemAudio' is assigned a value but never used.", "'audioSource' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "'beatDecay' is assigned a value but never used.", "'simulateAudio' is assigned a value but never used.", "'useEffect' is defined but never used.", "'connect' is assigned a value but never used."]