[{"C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js": "3", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js": "4", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js": "5", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js": "6", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js": "7", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js": "8", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js": "9", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js": "10", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js": "11", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js": "12", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js": "13", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js": "14", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\SystemAudioStatus.js": "15", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\usePythonAudioBackend.js": "16"}, {"size": 254, "mtime": 1750762253811, "results": "17", "hashOfConfig": "18"}, {"size": 2839, "mtime": 1751008229522, "results": "19", "hashOfConfig": "18"}, {"size": 5858, "mtime": 1750966557156, "results": "20", "hashOfConfig": "18"}, {"size": 13789, "mtime": 1751009551188, "results": "21", "hashOfConfig": "18"}, {"size": 910, "mtime": 1750784249896, "results": "22", "hashOfConfig": "18"}, {"size": 1166, "mtime": 1750784262464, "results": "23", "hashOfConfig": "18"}, {"size": 1566, "mtime": 1750784237894, "results": "24", "hashOfConfig": "18"}, {"size": 6839, "mtime": 1750966415822, "results": "25", "hashOfConfig": "18"}, {"size": 1577, "mtime": 1750784209479, "results": "26", "hashOfConfig": "18"}, {"size": 1673, "mtime": 1750784224221, "results": "27", "hashOfConfig": "18"}, {"size": 940, "mtime": 1750785802095, "results": "28", "hashOfConfig": "18"}, {"size": 9562, "mtime": 1750838136053, "results": "29", "hashOfConfig": "18"}, {"size": 13812, "mtime": 1750968668896, "results": "30", "hashOfConfig": "18"}, {"size": 20666, "mtime": 1751006687515, "results": "31", "hashOfConfig": "18"}, {"size": 2991, "mtime": 1751005740875, "results": "32", "hashOfConfig": "18"}, {"size": 7160, "mtime": 1751009533462, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1edix7f", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js", ["82"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js", ["83", "84", "85", "86"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\SystemAudioStatus.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\hooks\\usePythonAudioBackend.js", [], [], {"ruleId": "87", "severity": 1, "message": "88", "line": 18, "column": 5, "nodeType": "89", "messageId": "90", "endLine": 18, "endColumn": 18}, {"ruleId": "87", "severity": 1, "message": "88", "line": 18, "column": 5, "nodeType": "89", "messageId": "90", "endLine": 18, "endColumn": 18}, {"ruleId": "87", "severity": 1, "message": "91", "line": 20, "column": 5, "nodeType": "89", "messageId": "90", "endLine": 20, "endColumn": 18}, {"ruleId": "87", "severity": 1, "message": "92", "line": 21, "column": 5, "nodeType": "89", "messageId": "90", "endLine": 21, "endColumn": 16}, {"ruleId": "87", "severity": 1, "message": "93", "line": 22, "column": 5, "nodeType": "89", "messageId": "90", "endLine": 22, "endColumn": 16}, "no-unused-vars", "'isAudioActive' is assigned a value but never used.", "Identifier", "unusedVar", "'isSystemAudio' is assigned a value but never used.", "'audioSource' is assigned a value but never used.", "'isConnected' is assigned a value but never used."]