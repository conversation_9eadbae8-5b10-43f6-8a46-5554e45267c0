[{"C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js": "3", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js": "4", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js": "5", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js": "6", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js": "7", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js": "8", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js": "9", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js": "10", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js": "11", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js": "12", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js": "13", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js": "14"}, {"size": 254, "mtime": 1750762253811, "results": "15", "hashOfConfig": "16"}, {"size": 2839, "mtime": 1751008229522, "results": "17", "hashOfConfig": "16"}, {"size": 5858, "mtime": 1750966557156, "results": "18", "hashOfConfig": "16"}, {"size": 11793, "mtime": 1751022143156, "results": "19", "hashOfConfig": "16"}, {"size": 910, "mtime": 1750784249896, "results": "20", "hashOfConfig": "16"}, {"size": 1166, "mtime": 1750784262464, "results": "21", "hashOfConfig": "16"}, {"size": 1566, "mtime": 1750784237894, "results": "22", "hashOfConfig": "16"}, {"size": 6839, "mtime": 1750966415822, "results": "23", "hashOfConfig": "16"}, {"size": 1577, "mtime": 1750784209479, "results": "24", "hashOfConfig": "16"}, {"size": 1673, "mtime": 1750784224221, "results": "25", "hashOfConfig": "16"}, {"size": 940, "mtime": 1750785802095, "results": "26", "hashOfConfig": "16"}, {"size": 9562, "mtime": 1750838136053, "results": "27", "hashOfConfig": "16"}, {"size": 13812, "mtime": 1750968668896, "results": "28", "hashOfConfig": "16"}, {"size": 18133, "mtime": 1751022245383, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1edix7f", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js", ["72", "73", "74", "75"], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js", [], [], {"ruleId": "76", "severity": 2, "message": "77", "line": 89, "column": 10, "nodeType": "78", "messageId": "79", "endLine": 89, "endColumn": 27}, {"ruleId": "80", "severity": 2, "message": "81", "line": 90, "column": 26, "nodeType": "82", "messageId": "83", "endLine": 90, "endColumn": 39}, {"ruleId": "80", "severity": 2, "message": "84", "line": 91, "column": 24, "nodeType": "82", "messageId": "83", "endLine": 91, "endColumn": 35}, {"ruleId": "80", "severity": 2, "message": "85", "line": 92, "column": 23, "nodeType": "82", "messageId": "83", "endLine": 92, "endColumn": 33}, "react/jsx-no-undef", "'SystemAudioStatus' is not defined.", "JSXIdentifier", "undefined", "no-undef", "'isSystemAudio' is not defined.", "Identifier", "undef", "'audioSource' is not defined.", "'audioLevel' is not defined."]