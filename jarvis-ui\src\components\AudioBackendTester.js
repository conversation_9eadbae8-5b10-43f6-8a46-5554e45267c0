import React, { useState } from 'react';
import usePythonAudioBackend from '../hooks/usePythonAudioBackend';

const AudioBackendTester = () => {
  const [usePythonBackend, setUsePythonBackend] = useState(false);
  
  const audioData = usePythonAudioBackend({
    enabled: true,
    usePythonBackend: usePythonBackend,
    serverUrl: 'ws://localhost:8765', // C# backend
    pythonServerUrl: 'ws://localhost:8766', // Python backend
  });

  const {
    // Basic audio data
    audioLevel,
    bassLevel,
    midLevel,
    trebleLevel,
    beatDetected,
    isAudioActive,
    audioSource,
    isSystemAudio,

    // Advanced frequency bands
    subBassLevel,
    lowMidrangeLevel,
    upperMidrangeLevel,
    presenceLevel,
    brillianceLevel,

    // Advanced beat detection
    subBassBeat,
    bassBeat,
    lowMidrangeBeat,
    midrangeBeat,
    upperMidrangeBeat,
    presenceBeat,
    brillianceBeat,

    // Connection state
    isConnected,
    connectionError,
    reconnectAttempts,

    // Connection controls
    connect,
    disconnect,
  } = audioData;

  const handleBackendSwitch = (pythonMode) => {
    disconnect(); // Disconnect current backend
    setUsePythonBackend(pythonMode);
    setTimeout(() => {
      connect(); // Connect to new backend after state update
    }, 500);
  };

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '20px',
      borderRadius: '10px',
      fontFamily: 'monospace',
      fontSize: '12px',
      zIndex: 1000,
      minWidth: '300px',
      maxHeight: '80vh',
      overflowY: 'auto'
    }}>
      <h3 style={{ margin: '0 0 15px 0', color: '#00ff00' }}>
        🎵 Audio Backend Tester
      </h3>
      
      {/* Backend Selection */}
      <div style={{ marginBottom: '15px' }}>
        <div style={{ marginBottom: '10px', fontWeight: 'bold' }}>Backend Selection:</div>
        <button
          onClick={() => handleBackendSwitch(false)}
          style={{
            background: !usePythonBackend ? '#00ff00' : '#333',
            color: !usePythonBackend ? 'black' : 'white',
            border: 'none',
            padding: '5px 10px',
            marginRight: '10px',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          C# WASAPI
        </button>
        <button
          onClick={() => handleBackendSwitch(true)}
          style={{
            background: usePythonBackend ? '#00ff00' : '#333',
            color: usePythonBackend ? 'black' : 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Python FFT
        </button>
      </div>

      {/* Connection Status */}
      <div style={{ marginBottom: '15px' }}>
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Connection Status:</div>
        <div style={{ color: isConnected ? '#00ff00' : '#ff0000' }}>
          {isConnected ? '✅ Connected' : '❌ Disconnected'}
        </div>
        {connectionError && (
          <div style={{ color: '#ff0000', fontSize: '10px' }}>
            Error: {connectionError}
          </div>
        )}
        <div style={{ fontSize: '10px', color: '#888' }}>
          Backend: {usePythonBackend ? 'Python FFT (8766)' : 'C# WASAPI (8765)'}
        </div>
        <div style={{ fontSize: '10px', color: '#888' }}>
          Source: {audioSource}
        </div>
      </div>

      {/* Basic Audio Levels */}
      <div style={{ marginBottom: '15px' }}>
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Basic Audio Levels:</div>
        <div>Audio: {audioLevel.toFixed(1)}%</div>
        <div>Bass: {bassLevel.toFixed(1)}%</div>
        <div>Mid: {midLevel.toFixed(1)}%</div>
        <div>Treble: {trebleLevel.toFixed(1)}%</div>
        <div style={{ color: beatDetected ? '#ff0000' : '#666' }}>
          Beat: {beatDetected ? '🔥 DETECTED' : '⚪ None'}
        </div>
      </div>

      {/* Advanced Frequency Bands */}
      <div style={{ marginBottom: '15px' }}>
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Advanced Frequency Bands:</div>
        <div>Sub Bass (20-60Hz): {subBassLevel?.toFixed(1) || 0}%</div>
        <div>Low Mid (250-500Hz): {lowMidrangeLevel?.toFixed(1) || 0}%</div>
        <div>Upper Mid (2-4kHz): {upperMidrangeLevel?.toFixed(1) || 0}%</div>
        <div>Presence (4-6kHz): {presenceLevel?.toFixed(1) || 0}%</div>
        <div>Brilliance (6-20kHz): {brillianceLevel?.toFixed(1) || 0}%</div>
      </div>

      {/* Advanced Beat Detection */}
      <div style={{ marginBottom: '15px' }}>
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Advanced Beat Detection:</div>
        <div style={{ color: subBassBeat ? '#ff0000' : '#666' }}>
          Sub Bass: {subBassBeat ? '🔊' : '⚪'}
        </div>
        <div style={{ color: bassBeat ? '#ff0000' : '#666' }}>
          Bass: {bassBeat ? '🥁' : '⚪'}
        </div>
        <div style={{ color: lowMidrangeBeat ? '#ff0000' : '#666' }}>
          Low Mid: {lowMidrangeBeat ? '🎸' : '⚪'}
        </div>
        <div style={{ color: midrangeBeat ? '#ff0000' : '#666' }}>
          Midrange: {midrangeBeat ? '🎤' : '⚪'}
        </div>
        <div style={{ color: upperMidrangeBeat ? '#ff0000' : '#666' }}>
          Upper Mid: {upperMidrangeBeat ? '🎺' : '⚪'}
        </div>
        <div style={{ color: presenceBeat ? '#ff0000' : '#666' }}>
          Presence: {presenceBeat ? '✨' : '⚪'}
        </div>
        <div style={{ color: brillianceBeat ? '#ff0000' : '#666' }}>
          Brilliance: {brillianceBeat ? '💎' : '⚪'}
        </div>
      </div>

      {/* Controls */}
      <div>
        <button
          onClick={connect}
          disabled={isConnected}
          style={{
            background: '#00ff00',
            color: 'black',
            border: 'none',
            padding: '5px 10px',
            marginRight: '10px',
            borderRadius: '5px',
            cursor: isConnected ? 'not-allowed' : 'pointer',
            opacity: isConnected ? 0.5 : 1
          }}
        >
          Connect
        </button>
        <button
          onClick={disconnect}
          disabled={!isConnected}
          style={{
            background: '#ff0000',
            color: 'white',
            border: 'none',
            padding: '5px 10px',
            borderRadius: '5px',
            cursor: !isConnected ? 'not-allowed' : 'pointer',
            opacity: !isConnected ? 0.5 : 1
          }}
        >
          Disconnect
        </button>
      </div>
    </div>
  );
};

export default AudioBackendTester;
