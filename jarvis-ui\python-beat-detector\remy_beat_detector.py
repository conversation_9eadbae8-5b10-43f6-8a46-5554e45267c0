#!/usr/bin/env python3
"""
Remy2701 Beat Detection Backend
Using the sophisticated beat detection algorithm from https://github.com/Remy2701/beat_detection
"""

import asyncio
import websockets
import json
import numpy as np
import pyaudio
import threading
import time
import scipy.signal as signal
from collections import deque

class RemyBeatDetector:
    def __init__(self, sample_rate=44100, chunk_size=1024):
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.clients = set()
        
        # Audio buffer for processing
        self.audio_buffer = deque(maxlen=44100)  # 1 second buffer
        self.latest_audio_data = None
        
        # Beat detection parameters
        self.block_size = 1024
        self.window_size = 43
        self.max_bpm = 400
        
        # Debug counter
        self.audio_frame_count = 0
        
        print("🎵 Remy2701 Beat Detector initialized")
        print(f"📊 Sample Rate: {self.sample_rate} Hz")
        print(f"📦 Chunk Size: {self.chunk_size} samples")

    # Frequency Filters (from Remy2701's filters.py)
    def create_sub_filter(self, sr):
        """Create a filter to keep only the sub frequencies (0 - 60Hz)"""
        order = 5
        cutOff = 60.0
        return signal.butter(order, cutOff, fs=sr, btype='low', analog=False)

    def sub_filter(self, sr, y):
        """Apply the sub filter to the signal"""
        try:
            b, a = self.create_sub_filter(sr)
            # Ensure y is a proper numpy array for NumPy 2.0 compatibility
            y_array = np.asarray(y, dtype=np.float64)
            return signal.lfilter(b, a, y_array)
        except Exception as e:
            print(f"Error in sub_filter: {e}")
            return np.zeros_like(y)

    def create_low_filter(self, sr):
        """Create a filter to keep only the low frequencies (60 - 300Hz)"""
        order = 3
        cutOff1 = 60.0
        cutOff2 = 300.0
        return signal.butter(order, [cutOff1, cutOff2], fs=sr, btype='bandpass', analog=False)

    def low_filter(self, sr, y):
        """Apply the low filter to the signal"""
        try:
            b, a = self.create_low_filter(sr)
            # Ensure y is a proper numpy array for NumPy 2.0 compatibility
            y_array = np.asarray(y, dtype=np.float64)
            return signal.lfilter(b, a, y_array)
        except Exception as e:
            print(f"Error in low_filter: {e}")
            return np.zeros_like(y)

    def create_midrange_filter(self, sr):
        """Create a filter to keep only the midrange frequencies (300 - 2kHz)"""
        order = 5
        cutOff1 = 300.0
        cutOff2 = 2000.0
        return signal.butter(order, [cutOff1, cutOff2], fs=sr, btype='bandpass', analog=False)

    def midrange_filter(self, sr, y):
        """Apply the midrange filter to the signal"""
        try:
            b, a = self.create_midrange_filter(sr)
            # Ensure y is a proper numpy array for NumPy 2.0 compatibility
            y_array = np.asarray(y, dtype=np.float64)
            return signal.lfilter(b, a, y_array)
        except Exception as e:
            print(f"Error in midrange_filter: {e}")
            return np.zeros_like(y)

    # Beat Detection Core (from Remy2701's beat.py)
    def create_blocks(self, y, block_size=1024):
        """Divides the signal into blocks"""
        try:
            # Fix NumPy 2.0 compatibility - use int() to ensure integer division
            y_array = np.asarray(y, dtype=np.float64)
            num_blocks = max(1, int(len(y_array) / (block_size - 1)))
            return np.array_split(y_array, num_blocks)
        except Exception as e:
            print(f"Error in create_blocks: {e}")
            return [np.array([0.0])]

    def calculate_energy(self, blocks):
        """Computes the energy of each block"""
        try:
            return [np.sum(np.asarray(block, dtype=np.float64)**2) for block in blocks]
        except Exception as e:
            print(f"Error in calculate_energy: {e}")
            return [0.0]

    def moving_mean_single(self, energy, i, window_size=43):
        """Compute the moving average of the energy for a single block"""
        try:
            # Pure Python implementation to avoid NumPy 2.0 issues
            start_idx = max(0, i - window_size)
            end_idx = i + 1

            # Convert to list and calculate mean manually
            energy_list = list(energy) if not isinstance(energy, list) else energy
            window_values = energy_list[start_idx:end_idx]

            if not window_values:
                return 0.0

            mean_value = sum(window_values) / len(window_values)
            return float(mean_value) if not (mean_value != mean_value) else 0.0  # Check for NaN
        except Exception as e:
            print(f"Error in moving_mean_single: {e}")
            return 0.0

    def moving_mean(self, energy, window_size=43):
        """Compute the moving average of the energy of each block"""
        try:
            result = [self.moving_mean_single(energy, i, window_size) for i in range(len(energy))]
            return np.asarray(result, dtype=np.float64)
        except Exception as e:
            print(f"Error in moving_mean: {e}")
            return np.zeros(len(energy), dtype=np.float64)

    def variance_single(self, energy, i):
        """Compute the variance of the energy for a single block"""
        try:
            # Pure Python implementation to avoid NumPy 2.0 issues
            energy_list = list(energy) if not isinstance(energy, list) else energy
            if i == 0:
                return float(energy_list[i])
            else:
                return float(max(0, (energy_list[i - 1] - energy_list[i])))
        except Exception as e:
            print(f"Error in variance_single: {e}")
            return 0.0

    def variance(self, energy):
        """Compute the variance of the energy of each block"""
        try:
            result = [self.variance_single(energy, i) for i in range(len(energy))]
            return np.asarray(result, dtype=np.float64)
        except Exception as e:
            print(f"Error in variance: {e}")
            return np.zeros(len(energy), dtype=np.float64)

    def is_beat_single(self, variance, avg, i):
        """Detect whether a single block is a beat"""
        try:
            # Pure Python implementation to avoid NumPy 2.0 issues
            variance_list = list(variance) if not isinstance(variance, list) else variance
            avg_list = list(avg) if not isinstance(avg, list) else avg

            if variance_list[i] > avg_list[i] and (i == 0 or variance_list[i - 1] < avg_list[i - 1]):
                return 1
            else:
                return 0
        except Exception as e:
            print(f"Error in is_beat_single: {e}")
            return 0

    def detect_beats_in_blocks(self, variance, avg):
        """Detect the beats in the signal for each blocks"""
        try:
            result = [self.is_beat_single(variance, avg, i) for i in range(len(variance))]
            return np.asarray(result, dtype=np.int32)
        except Exception as e:
            print(f"Error in detect_beats_in_blocks: {e}")
            return np.zeros(len(variance), dtype=np.int32)

    def detect_beats_in_frequency_range(self, y, freq_range='sub'):
        """Detect beats in a specific frequency range"""
        try:
            # Filter the signal
            if freq_range == 'sub':
                y_filtered = self.sub_filter(self.sample_rate, y)
            elif freq_range == 'low':
                y_filtered = self.low_filter(self.sample_rate, y)
            elif freq_range == 'mid':
                y_filtered = self.midrange_filter(self.sample_rate, y)
            else:
                y_filtered = y

            # Split the signal into blocks
            blocks = self.create_blocks(y_filtered, self.block_size)
            
            if len(blocks) < 2:
                return 0, 0  # Not enough data
            
            # Compute the energy of each block
            energy = self.calculate_energy(blocks)
            
            if len(energy) < self.window_size:
                return 0, 0  # Not enough data for window
            
            # Compute the moving average of the energy
            energy_block_avg = self.moving_mean(energy, self.window_size)
            
            # Compute the variance of the energy
            energy_block_variance = self.variance(energy)
            
            # Detect the beats in the signal
            beats = self.detect_beats_in_blocks(energy_block_variance, energy_block_avg)
            
            # Calculate beat strength and frequency level
            beat_strength = np.sum(beats) / len(beats) if len(beats) > 0 else 0
            frequency_level = np.sqrt(np.mean(y_filtered**2)) if len(y_filtered) > 0 else 0
            
            return beat_strength, frequency_level
            
        except Exception as e:
            print(f"❌ Beat detection error in {freq_range}: {e}")
            return 0, 0

    def process_audio_chunk(self, in_data):
        """Process incoming audio data using Remy2701 algorithm"""
        try:
            # Convert audio data to numpy array
            audio_data = np.frombuffer(in_data, dtype=np.float32)
            
            # Add to buffer
            self.audio_buffer.extend(audio_data)
            
            # Need enough data for processing
            if len(self.audio_buffer) < self.sample_rate // 2:  # 0.5 seconds minimum
                return None
            
            # Convert buffer to numpy array with explicit dtype for NumPy 2.0 compatibility
            y = np.asarray(list(self.audio_buffer), dtype=np.float64)
            
            # Detect beats in different frequency ranges
            sub_beat, sub_level = self.detect_beats_in_frequency_range(y, 'sub')
            low_beat, low_level = self.detect_beats_in_frequency_range(y, 'low')
            mid_beat, mid_level = self.detect_beats_in_frequency_range(y, 'mid')
            
            # Calculate overall audio level
            audio_level = np.sqrt(np.mean(y**2))
            
            # Combine beat detection (sub beats are more important)
            combined_beat = max(sub_beat * 2, low_beat, mid_beat)
            
            # Create comprehensive audio data
            audio_data = {
                'type': 'audio',
                'timestamp': time.time(),
                'source': 'Remy2701 Algorithm',
                
                # Overall metrics
                'audioLevel': min(100, audio_level * 1000),  # Scale for visibility
                'beatDetected': combined_beat > 0.1,
                'beatStrength': min(100, combined_beat * 100),
                
                # Frequency-specific levels
                'subBassLevel': min(100, sub_level * 1000),
                'bassLevel': min(100, low_level * 1000),
                'midLevel': min(100, mid_level * 1000),
                
                # Frequency-specific beats
                'subBassBeat': sub_beat > 0.1,
                'bassBeat': low_beat > 0.1,
                'midBeat': mid_beat > 0.1,
                
                # Algorithm-specific data
                'algorithm': 'Remy2701 Energy-Based',
                'blockSize': self.block_size,
                'windowSize': self.window_size,
                'bufferSize': len(self.audio_buffer)
            }
            
            return audio_data
            
        except Exception as e:
            print(f"❌ Audio processing error: {e}")
            return None

    def audio_callback(self, in_data, frame_count, time_info, status):
        """PyAudio callback function"""
        # Process audio data
        audio_data = self.process_audio_chunk(in_data)
        
        # Store data for broadcasting
        if audio_data:
            self.latest_audio_data = audio_data
            
        # Debug output every 100 frames
        self.audio_frame_count += 1
        if self.audio_frame_count % 100 == 0:
            if audio_data:
                print(f"🎵 Frame {self.audio_frame_count}: Audio={audio_data.get('audioLevel', 0):.1f}%, Beat={audio_data.get('beatStrength', 0):.1f}%")
            else:
                print(f"🔇 Frame {self.audio_frame_count}: No audio data")
        
        return (in_data, pyaudio.paContinue)

    async def websocket_handler(self, websocket, path):
        """Handle WebSocket connections"""
        print(f"🔌 Remy2701 WebSocket connection from {websocket.remote_address}")
        self.clients.add(websocket)
        
        try:
            # Send connection confirmation
            await websocket.send(json.dumps({
                'type': 'connection',
                'status': 'connected',
                'message': 'Connected to Remy2701 Beat Detector',
                'source': 'Remy2701 Energy-Based Algorithm',
                'method': 'PyAudio + Scipy Filters + Energy Analysis'
            }))
            
            await websocket.wait_closed()
        except Exception as e:
            print(f"❌ WebSocket error: {e}")
        finally:
            self.clients.discard(websocket)
            print(f"🔌 Remy2701 WebSocket connection closed")

    async def broadcast_audio_data(self, data):
        """Broadcast audio data to all connected clients"""
        if self.clients and data:
            message = json.dumps(data)
            disconnected = set()
            
            for client in self.clients:
                try:
                    await client.send(message)
                except Exception as e:
                    disconnected.add(client)
            
            # Remove disconnected clients
            self.clients -= disconnected

    async def start_audio_capture(self):
        """Start audio capture in a separate thread"""
        def audio_thread():
            try:
                # Initialize PyAudio
                p = pyaudio.PyAudio()

                print("🎵 Starting Remy2701 Beat Detector...")
                print("🔍 Available audio devices:")

                # List audio devices and find Stereo Mix
                stereo_mix_device = None
                for i in range(p.get_device_count()):
                    info = p.get_device_info_by_index(i)
                    if info['maxInputChannels'] > 0:
                        print(f"  📱 {i}: {info['name']}")
                        # Look for Stereo Mix device for system audio capture
                        if 'stereo mix' in info['name'].lower():
                            stereo_mix_device = i
                            print(f"  🎯 Found Stereo Mix device: {i}")

                # Try different approaches for audio input
                stream = None

                # First try: Use default device (microphone)
                try:
                    print("🎤 Trying default microphone...")
                    stream = p.open(
                        format=pyaudio.paFloat32,
                        channels=1,  # Mono
                        rate=self.sample_rate,
                        input=True,
                        frames_per_buffer=self.chunk_size,
                        stream_callback=self.audio_callback
                    )
                    print("✅ Using default microphone")
                except Exception as e:
                    print(f"❌ Default microphone failed: {e}")

                # If default failed, try Stereo Mix devices
                if stream is None and stereo_mix_device is not None:
                    try:
                        print(f"🎵 Trying Stereo Mix device {stereo_mix_device}...")
                        stream = p.open(
                            format=pyaudio.paFloat32,
                            channels=1,  # Mono
                            rate=self.sample_rate,
                            input=True,
                            input_device_index=stereo_mix_device,
                            frames_per_buffer=self.chunk_size,
                            stream_callback=self.audio_callback
                        )
                        print(f"✅ Using Stereo Mix device {stereo_mix_device}")
                    except Exception as e:
                        print(f"❌ Stereo Mix device {stereo_mix_device} failed: {e}")

                if stream is None:
                    raise Exception("❌ No working audio input device found!")

                print("✅ Audio capture started!")
                print("🎵 Listening for beats with Remy2701 algorithm...")

                # Keep the stream active
                stream.start_stream()
                while stream.is_active():
                    time.sleep(0.1)

            except Exception as e:
                print(f"❌ Audio capture error: {e}")
            finally:
                if 'stream' in locals():
                    stream.stop_stream()
                    stream.close()
                if 'p' in locals():
                    p.terminate()

        # Start audio capture in background thread
        audio_thread_obj = threading.Thread(target=audio_thread, daemon=True)
        audio_thread_obj.start()

    async def start_server(self):
        """Start the WebSocket server and audio capture"""
        print("🚀 Remy2701 Beat Detector Server")
        print("=" * 50)
        print("🎵 Using Remy2701's energy-based algorithm!")
        print("📊 Frequency filtering with Scipy")
        print("🔊 Advanced beat detection with energy variance")

        # Start WebSocket server
        server = await websockets.serve(
            self.websocket_handler,
            "localhost",
            8767,  # Different port from other backends
            ping_interval=20,
            ping_timeout=10
        )

        print("🌐 WebSocket server started on ws://localhost:8767")
        print("🔗 Connect your frontend to this URL for Remy2701 beat detection")
        print("⚡ Server is running... Press Ctrl+C to stop")

        # Start audio capture
        await self.start_audio_capture()

        # Broadcasting loop
        async def broadcast_loop():
            while True:
                if self.latest_audio_data and self.clients:
                    await self.broadcast_audio_data(self.latest_audio_data)
                await asyncio.sleep(0.05)  # 20 FPS

        # Start broadcasting
        broadcast_task = asyncio.create_task(broadcast_loop())

        # Keep server running
        await server.wait_closed()

async def main():
    """Main function"""
    detector = RemyBeatDetector()
    await detector.start_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Remy2701 Beat Detector stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
