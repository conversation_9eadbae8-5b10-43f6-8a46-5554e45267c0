#!/usr/bin/env python3
"""
Test script to check if Stereo Mix is working properly
"""

import pyaudio
import numpy as np
import time

def test_stereo_mix():
    """Test Stereo Mix audio capture"""
    print("🎵 Testing Stereo Mix Audio Capture")
    print("=" * 50)
    
    # Initialize PyAudio
    p = pyaudio.PyAudio()
    
    # List all audio devices
    print("🔍 Available audio devices:")
    stereo_mix_devices = []
    
    for i in range(p.get_device_count()):
        try:
            info = p.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                print(f"  📱 {i}: {info['name']} (Channels: {info['maxInputChannels']}, Rate: {info['defaultSampleRate']})")
                if 'stereo mix' in info['name'].lower():
                    stereo_mix_devices.append(i)
                    print(f"    🎯 STEREO MIX FOUND!")
        except Exception as e:
            print(f"    ❌ Error reading device {i}: {e}")
    
    if not stereo_mix_devices:
        print("\n❌ No Stereo Mix devices found!")
        print("💡 To enable Stereo Mix:")
        print("   1. Right-click sound icon in system tray")
        print("   2. Select 'Open Sound settings'")
        print("   3. Click 'Sound Control Panel'")
        print("   4. Go to 'Recording' tab")
        print("   5. Right-click empty space, select 'Show Disabled Devices'")
        print("   6. Right-click 'Stereo Mix', select 'Enable'")
        print("   7. Set as default recording device")
        p.terminate()
        return
    
    # Test each Stereo Mix device
    for device_id in stereo_mix_devices:
        print(f"\n🎯 Testing Stereo Mix device {device_id}...")
        
        try:
            # Open audio stream
            stream = p.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=44100,
                input=True,
                input_device_index=device_id,
                frames_per_buffer=1024
            )
            
            print("✅ Stream opened successfully!")
            print("🎵 Listening for 10 seconds... Play some audio!")
            
            # Capture audio for 10 seconds
            max_level = 0
            total_samples = 0
            non_zero_samples = 0
            
            start_time = time.time()
            while time.time() - start_time < 10:
                try:
                    data = stream.read(1024, exception_on_overflow=False)
                    audio_data = np.frombuffer(data, dtype=np.float32)
                    
                    # Calculate audio level
                    level = np.sqrt(np.mean(audio_data**2))
                    max_level = max(max_level, level)
                    
                    total_samples += len(audio_data)
                    non_zero_samples += np.count_nonzero(audio_data)
                    
                    # Print level every second
                    if int(time.time() - start_time) != int(time.time() - start_time - 0.1):
                        percentage = level * 100
                        print(f"  📊 Audio Level: {percentage:.1f}% (Max: {max_level*100:.1f}%)")
                        
                except Exception as e:
                    print(f"  ❌ Read error: {e}")
                    break
            
            stream.stop_stream()
            stream.close()
            
            # Results
            print(f"\n📊 Test Results for device {device_id}:")
            print(f"  🔊 Maximum audio level: {max_level*100:.1f}%")
            print(f"  📈 Non-zero samples: {non_zero_samples}/{total_samples} ({non_zero_samples/total_samples*100:.1f}%)")
            
            if max_level > 0.01:
                print("  ✅ STEREO MIX IS WORKING!")
            elif non_zero_samples > 0:
                print("  ⚠️  Audio detected but very quiet")
            else:
                print("  ❌ No audio detected - Stereo Mix may not be enabled")
                
        except Exception as e:
            print(f"  ❌ Failed to test device {device_id}: {e}")
    
    p.terminate()
    print("\n🏁 Test completed!")

if __name__ == "__main__":
    test_stereo_mix()
