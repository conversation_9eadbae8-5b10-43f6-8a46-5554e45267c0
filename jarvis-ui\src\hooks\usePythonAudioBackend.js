import { useState, useEffect, useRef, useCallback } from 'react';

const usePythonAudioBackend = ({ 
  enabled = true, 
  serverUrl = 'ws://localhost:8765',
  reconnectInterval = 3000,
  maxReconnectAttempts = 10
}) => {
  // Audio data state
  const [audioLevel, setAudioLevel] = useState(0);
  const [bassLevel, setBassLevel] = useState(0);
  const [midLevel, setMidLevel] = useState(0);
  const [trebleLevel, setTrebleLevel] = useState(0);
  const [beatDetected, setBeatDetected] = useState(false);
  const [audioSource, setAudioSource] = useState('none');
  const [isSystemAudio, setIsSystemAudio] = useState(false);
  
  // Connection state
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  // Refs
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const lastDataRef = useRef(null);
  
  // Beat detection smoothing
  const beatTimeoutRef = useRef(null);
  
  const connect = useCallback(() => {
    if (!enabled) {
      console.log('🚫 Python audio backend disabled');
      return;
    }
    
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('🔌 WebSocket already connected');
      return;
    }
    
    try {
      console.log(`🔌 Connecting to Python audio backend: ${serverUrl}`);
      
      const ws = new WebSocket(serverUrl);
      wsRef.current = ws;
      
      ws.onopen = () => {
        console.log('✅ Connected to Python audio backend');
        setIsConnected(true);
        setConnectionError(null);
        setReconnectAttempts(0);
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          // Update audio data
          setAudioLevel(data.audioLevel || 0);
          setBassLevel(data.bassLevel || 0);
          setMidLevel(data.midLevel || 0);
          setTrebleLevel(data.trebleLevel || 0);
          setAudioSource(data.audioSource || 'none');
          setIsSystemAudio(data.isSystemAudio || false);
          
          // Handle beat detection with visual feedback
          if (data.beatDetected) {
            setBeatDetected(true);
            
            // Clear previous timeout
            if (beatTimeoutRef.current) {
              clearTimeout(beatTimeoutRef.current);
            }
            
            // Reset beat detection after 200ms
            beatTimeoutRef.current = setTimeout(() => {
              setBeatDetected(false);
            }, 200);
          }
          
          lastDataRef.current = data;
          
        } catch (error) {
          console.error('❌ Error parsing audio data:', error);
        }
      };
      
      ws.onclose = (event) => {
        console.log('🔌 WebSocket connection closed:', event.code, event.reason);
        setIsConnected(false);
        wsRef.current = null;
        
        // Attempt to reconnect if enabled and not manually closed
        if (enabled && event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          console.log(`🔄 Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`);
          setReconnectAttempts(prev => prev + 1);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        } else if (reconnectAttempts >= maxReconnectAttempts) {
          setConnectionError('Max reconnection attempts reached');
          console.error('❌ Max reconnection attempts reached');
        }
      };
      
      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionError('Connection failed');
      };
      
    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      setConnectionError(error.message);
    }
  }, [enabled, serverUrl, reconnectInterval, maxReconnectAttempts, reconnectAttempts]);
  
  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting from Python audio backend');
    
    // Clear reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    // Clear beat timeout
    if (beatTimeoutRef.current) {
      clearTimeout(beatTimeoutRef.current);
      beatTimeoutRef.current = null;
    }
    
    // Close WebSocket connection
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    
    // Reset state
    setIsConnected(false);
    setConnectionError(null);
    setReconnectAttempts(0);
    setAudioLevel(0);
    setBassLevel(0);
    setMidLevel(0);
    setTrebleLevel(0);
    setBeatDetected(false);
    setAudioSource('none');
    setIsSystemAudio(false);
  }, []);
  
  const reconnect = useCallback(() => {
    console.log('🔄 Manual reconnection requested');
    disconnect();
    setTimeout(() => {
      setReconnectAttempts(0);
      connect();
    }, 1000);
  }, [disconnect, connect]);
  
  // Auto-connect when enabled
  useEffect(() => {
    if (enabled) {
      connect();
    } else {
      disconnect();
    }
    
    return () => {
      disconnect();
    };
  }, [enabled, connect, disconnect]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);
  
  // Connection status helper
  const getConnectionStatus = useCallback(() => {
    if (isConnected) {
      return {
        status: 'connected',
        message: `Connected to Python backend (${isSystemAudio ? 'System Audio' : 'Microphone'})`
      };
    } else if (connectionError) {
      return {
        status: 'error',
        message: `Connection error: ${connectionError}`
      };
    } else if (reconnectAttempts > 0) {
      return {
        status: 'reconnecting',
        message: `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`
      };
    } else {
      return {
        status: 'disconnected',
        message: 'Not connected to Python backend'
      };
    }
  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, isSystemAudio]);
  
  // Debug info
  const getDebugInfo = useCallback(() => {
    return {
      isConnected,
      connectionError,
      reconnectAttempts,
      maxReconnectAttempts,
      serverUrl,
      lastData: lastDataRef.current,
      wsReadyState: wsRef.current?.readyState
    };
  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, serverUrl]);
  
  // Computed values - Maximum sensitivity
  const isAudioActive = audioLevel > 0.5; // Consider audio active if level > 0.5%

  return {
    // Audio data
    audioLevel,
    bassLevel,
    midLevel,
    trebleLevel,
    beatDetected,
    isAudioActive,
    audioSource,
    isSystemAudio,

    // Connection state
    isConnected,
    connectionError,
    reconnectAttempts,

    // Connection controls
    connect,
    disconnect,
    reconnect,

    // Helpers
    getConnectionStatus,
    getDebugInfo
  };
};

export default usePythonAudioBackend;
