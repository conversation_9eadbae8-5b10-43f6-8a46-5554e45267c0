import React, { useState, useEffect } from 'react';
import usePythonAudioBackend from '../../hooks/usePythonAudioBackend';

const AudioDebugPanel = ({ isVisible = false }) => {
  const [testResults, setTestResults] = useState({});
  const [isRunningTests, setIsRunningTests] = useState(false);

  const {
    audioLevel,
    bassLevel,
    midLevel,
    trebleLevel,
    beatDetected,
    audioSource,
    isSystemAudio,
    isConnected,
    connectionError,
    reconnectAttempts,
    connect,
    disconnect,
    reconnect,
    getConnectionStatus,
    getDebugInfo
  } = usePythonAudioBackend({
    enabled: isVisible, // Only connect when panel is visible
    serverUrl: 'ws://localhost:8765'
  });

  const runAudioTests = async () => {
    setIsRunningTests(true);
    const results = {};

    // Test 1: Check if Python backend is running
    results.pythonBackendRunning = isConnected;
    
    // Test 2: Check WebSocket support
    results.webSocketSupported = typeof WebSocket !== 'undefined';
    
    // Test 3: Connection status
    const status = getConnectionStatus();
    results.connectionStatus = status.status;
    results.connectionMessage = status.message;
    
    // Test 4: Debug info
    const debugInfo = getDebugInfo();
    results.debugInfo = debugInfo;
    
    // Test 5: Audio data availability
    results.audioDataAvailable = audioLevel > 0 || bassLevel > 0 || midLevel > 0 || trebleLevel > 0;
    
    setTestResults(results);
    setIsRunningTests(false);
  };

  const testBeepAudio = () => {
    // Generate a test beep to see if audio detection works
    try {
      const audioContext = new (window.AudioContext || window['webkitAudioContext'])();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4 note
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.5);
    } catch (error) {
      console.error('Failed to generate test beep:', error);
    }
  };

  if (!isVisible) return null;

  const connectionStatus = getConnectionStatus();

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '400px',
      maxHeight: '80vh',
      backgroundColor: 'rgba(0, 20, 40, 0.95)',
      border: '1px solid #00ffff',
      borderRadius: '8px',
      padding: '15px',
      color: '#00ffff',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 10000,
      overflow: 'auto'
    }}>
      <h3 style={{ margin: '0 0 15px 0', color: '#00ffff' }}>🎵 C# NVIDIA HDMI Audio Backend</h3>
      
      {/* Connection Status */}
      <div style={{ 
        marginBottom: '15px', 
        padding: '10px', 
        backgroundColor: isConnected ? 'rgba(0, 255, 0, 0.1)' : 'rgba(255, 0, 0, 0.1)', 
        borderRadius: '4px' 
      }}>
        <div><strong>Connection:</strong> {connectionStatus.status.toUpperCase()}</div>
        <div><strong>Message:</strong> {connectionStatus.message}</div>
        {reconnectAttempts > 0 && (
          <div><strong>Reconnect Attempts:</strong> {reconnectAttempts}</div>
        )}
        {connectionError && (
          <div style={{ color: '#ff4444' }}><strong>Error:</strong> {connectionError}</div>
        )}
      </div>

      {/* Current Audio Status */}
      {isConnected && (
        <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: 'rgba(0, 255, 255, 0.1)', borderRadius: '4px' }}>
          <div><strong>Status:</strong> {audioSource === 'none' ? 'NO AUDIO' : 'CAPTURING'}</div>
          <div><strong>Source:</strong> {isSystemAudio ? 'NVIDIA HDMI (C# WASAPI)' : 'Microphone Fallback'}</div>
          <div><strong>Overall Level:</strong> {(audioLevel * 100).toFixed(1)}%</div>
          <div><strong>Bass:</strong> {(bassLevel * 100).toFixed(1)}%</div>
          <div><strong>Mid:</strong> {(midLevel * 100).toFixed(1)}%</div>
          <div><strong>Treble:</strong> {(trebleLevel * 100).toFixed(1)}%</div>
          <div><strong>Beat:</strong> {beatDetected ? '🔥 DETECTED' : '⚪ None'}</div>
        </div>
      )}

      {/* Frequency Bars */}
      {isConnected && (
        <div style={{ marginBottom: '15px' }}>
          <div style={{ marginBottom: '5px' }}>
            <span>Bass: </span>
            <div style={{ 
              display: 'inline-block', 
              width: '100px', 
              height: '10px', 
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '5px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${bassLevel * 100}%`,
                height: '100%',
                backgroundColor: '#ff4444',
                transition: 'width 0.1s'
              }} />
            </div>
          </div>
          <div style={{ marginBottom: '5px' }}>
            <span>Mid: </span>
            <div style={{ 
              display: 'inline-block', 
              width: '100px', 
              height: '10px', 
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '5px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${midLevel * 100}%`,
                height: '100%',
                backgroundColor: '#44ff44',
                transition: 'width 0.1s'
              }} />
            </div>
          </div>
          <div style={{ marginBottom: '5px' }}>
            <span>Treble: </span>
            <div style={{ 
              display: 'inline-block', 
              width: '100px', 
              height: '10px', 
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '5px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${trebleLevel * 100}%`,
                height: '100%',
                backgroundColor: '#4444ff',
                transition: 'width 0.1s'
              }} />
            </div>
          </div>
        </div>
      )}

      {/* Test Controls */}
      <div style={{ marginBottom: '15px' }}>
        <button 
          onClick={runAudioTests}
          disabled={isRunningTests}
          style={{
            backgroundColor: '#00ffff',
            color: '#000',
            border: 'none',
            padding: '8px 12px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px',
            fontSize: '11px'
          }}
        >
          {isRunningTests ? 'Testing...' : 'Run Tests'}
        </button>
        <button 
          onClick={testBeepAudio}
          style={{
            backgroundColor: '#ffaa00',
            color: '#000',
            border: 'none',
            padding: '8px 12px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px',
            fontSize: '11px'
          }}
        >
          Test Beep
        </button>
        {isConnected ? (
          <button 
            onClick={disconnect}
            style={{
              backgroundColor: '#ff4444',
              color: '#fff',
              border: 'none',
              padding: '8px 12px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            Disconnect
          </button>
        ) : (
          <button 
            onClick={reconnect}
            style={{
              backgroundColor: '#44ff44',
              color: '#000',
              border: 'none',
              padding: '8px 12px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            Connect
          </button>
        )}
      </div>

      {/* Test Results */}
      {Object.keys(testResults).length > 0 && (
        <div style={{ fontSize: '11px' }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#ffaa00' }}>Test Results:</h4>
          {Object.entries(testResults).map(([key, value]) => (
            <div key={key} style={{ marginBottom: '3px' }}>
              <strong>{key}:</strong> {
                typeof value === 'boolean' 
                  ? (value ? '✅ Yes' : '❌ No')
                  : typeof value === 'object'
                  ? JSON.stringify(value, null, 2)
                  : String(value)
              }
            </div>
          ))}
        </div>
      )}

      {/* Instructions */}
      <div style={{ marginTop: '15px', fontSize: '10px', color: '#aaa' }}>
        <strong>Instructions:</strong><br/>
        1. Make sure C# backend is running: <code>dotnet run</code><br/>
        2. NVIDIA HDMI audio automatically detected via WASAPI<br/>
        3. Play music to test audio-reactive JARVIS effects<br/>
        4. Backend URL: ws://localhost:8765 (C# NAudio WASAPI)
      </div>
    </div>
  );
};

export default AudioDebugPanel;
