"""
Advanced Beat Detection using FFT Analysis
Based on maxemitchell/beat-detection-python
Modified for real-time system audio capture and WebSocket streaming
"""

import pyaudio
import numpy as np
import asyncio
import websockets
import json
import threading
import time
from collections import deque

class AdvancedBeatDetector:
    def __init__(self, sample_rate=44100, chunk_size=1024):
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        
        # Frequency band maximums (adaptive)
        self.sub_bass_max = 0.01
        self.bass_max = 0.01
        self.low_midrange_max = 0.01
        self.midrange_max = 0.01
        self.upper_midrange_max = 0.01
        self.presence_max = 0.01
        self.brilliance_max = 0.01
        
        # Beat detection states
        self.sub_bass_beat = False
        self.bass_beat = False
        self.low_midrange_beat = False
        self.midrange_beat = False
        self.upper_midrange_beat = False
        self.presence_beat = False
        self.brilliance_beat = False
        
        # Audio buffer for processing
        self.audio_buffer = deque(maxlen=self.chunk_size)
        
        # WebSocket clients
        self.clients = set()
        
        print("🎵 Advanced Beat Detector initialized")
        print(f"📊 Sample Rate: {sample_rate} Hz")
        print(f"📦 Chunk Size: {chunk_size} samples")

    def get_frequency_ranges(self, fft_length):
        """Generate frequency array for FFT analysis"""
        freqs = np.fft.fftfreq(fft_length, 1/self.sample_rate)
        return freqs[:fft_length//2]  # Only positive frequencies

    def extract_frequency_bands(self, audio_fft, freqs):
        """Extract energy levels for each frequency band"""
        # Frequency band indices
        sub_bass_indices = [idx for idx, val in enumerate(freqs) if 20 <= val <= 60]
        bass_indices = [idx for idx, val in enumerate(freqs) if 60 <= val <= 250]
        low_midrange_indices = [idx for idx, val in enumerate(freqs) if 250 <= val <= 500]
        midrange_indices = [idx for idx, val in enumerate(freqs) if 500 <= val <= 2000]
        upper_midrange_indices = [idx for idx, val in enumerate(freqs) if 2000 <= val <= 4000]
        presence_indices = [idx for idx, val in enumerate(freqs) if 4000 <= val <= 6000]
        brilliance_indices = [idx for idx, val in enumerate(freqs) if 6000 <= val <= 20000]
        
        # Extract maximum energy in each band
        sub_bass = np.max(audio_fft[sub_bass_indices]) if sub_bass_indices else 0
        bass = np.max(audio_fft[bass_indices]) if bass_indices else 0
        low_midrange = np.max(audio_fft[low_midrange_indices]) if low_midrange_indices else 0
        midrange = np.max(audio_fft[midrange_indices]) if midrange_indices else 0
        upper_midrange = np.max(audio_fft[upper_midrange_indices]) if upper_midrange_indices else 0
        presence = np.max(audio_fft[presence_indices]) if presence_indices else 0
        brilliance = np.max(audio_fft[brilliance_indices]) if brilliance_indices else 0
        
        return {
            'sub_bass': sub_bass,
            'bass': bass,
            'low_midrange': low_midrange,
            'midrange': midrange,
            'upper_midrange': upper_midrange,
            'presence': presence,
            'brilliance': brilliance
        }

    def detect_beats(self, bands):
        """Detect beats in each frequency band using adaptive thresholds"""
        # Update maximums
        self.sub_bass_max = max(self.sub_bass_max, bands['sub_bass'])
        self.bass_max = max(self.bass_max, bands['bass'])
        self.low_midrange_max = max(self.low_midrange_max, bands['low_midrange'])
        self.midrange_max = max(self.midrange_max, bands['midrange'])
        self.upper_midrange_max = max(self.upper_midrange_max, bands['upper_midrange'])
        self.presence_max = max(self.presence_max, bands['presence'])
        self.brilliance_max = max(self.brilliance_max, bands['brilliance'])
        
        # Beat detection with hysteresis (90% trigger, 30% reset)
        beat_threshold = 0.90
        reset_threshold = 0.30
        
        # Sub Bass Beat Detection
        if bands['sub_bass'] >= self.sub_bass_max * beat_threshold and not self.sub_bass_beat:
            self.sub_bass_beat = True
            print("🔊 Sub Bass Beat Detected!")
        elif bands['sub_bass'] < self.sub_bass_max * reset_threshold:
            self.sub_bass_beat = False
            
        # Bass Beat Detection
        if bands['bass'] >= self.bass_max * beat_threshold and not self.bass_beat:
            self.bass_beat = True
            print("🥁 Bass Beat Detected!")
        elif bands['bass'] < self.bass_max * reset_threshold:
            self.bass_beat = False
            
        # Low Midrange Beat Detection
        if bands['low_midrange'] >= self.low_midrange_max * beat_threshold and not self.low_midrange_beat:
            self.low_midrange_beat = True
            print("🎸 Low Midrange Beat Detected!")
        elif bands['low_midrange'] < self.low_midrange_max * reset_threshold:
            self.low_midrange_beat = False
            
        # Midrange Beat Detection
        if bands['midrange'] >= self.midrange_max * beat_threshold and not self.midrange_beat:
            self.midrange_beat = True
            print("🎤 Midrange Beat Detected!")
        elif bands['midrange'] < self.midrange_max * reset_threshold:
            self.midrange_beat = False
            
        # Upper Midrange Beat Detection
        if bands['upper_midrange'] >= self.upper_midrange_max * beat_threshold and not self.upper_midrange_beat:
            self.upper_midrange_beat = True
            print("🎺 Upper Midrange Beat Detected!")
        elif bands['upper_midrange'] < self.upper_midrange_max * reset_threshold:
            self.upper_midrange_beat = False
            
        # Presence Beat Detection
        if bands['presence'] >= self.presence_max * beat_threshold and not self.presence_beat:
            self.presence_beat = True
            print("✨ Presence Beat Detected!")
        elif bands['presence'] < self.presence_max * reset_threshold:
            self.presence_beat = False
            
        # Brilliance Beat Detection
        if bands['brilliance'] >= self.brilliance_max * beat_threshold and not self.brilliance_beat:
            self.brilliance_beat = True
            print("💎 Brilliance Beat Detected!")
        elif bands['brilliance'] < self.brilliance_max * reset_threshold:
            self.brilliance_beat = False

    def process_audio_chunk(self, audio_data):
        """Process audio chunk and return analysis results"""
        try:
            # Convert to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.float32)
            
            # Perform FFT
            audio_fft = np.abs(np.fft.fft(audio_array))
            audio_fft = audio_fft[:len(audio_fft)//2] / len(audio_array)  # Normalize
            
            # Generate frequency array
            freqs = self.get_frequency_ranges(len(audio_array))
            
            # Extract frequency bands
            bands = self.extract_frequency_bands(audio_fft, freqs)
            
            # Detect beats
            self.detect_beats(bands)
            
            # Calculate overall levels (0-100 scale)
            audio_level = min((bands['bass'] + bands['midrange'] + bands['upper_midrange']) * 300, 100.0)
            bass_level = min(bands['bass'] * 300, 100.0)
            mid_level = min(bands['midrange'] * 300, 100.0)
            treble_level = min((bands['upper_midrange'] + bands['presence'] + bands['brilliance']) * 300, 100.0)
            
            # Overall beat detection
            overall_beat = self.bass_beat or self.midrange_beat or self.upper_midrange_beat
            
            return {
                'type': 'audioData',
                'audioLevel': audio_level,
                'bassLevel': bass_level,
                'midLevel': mid_level,
                'trebleLevel': treble_level,
                'beatDetected': overall_beat,
                'source': 'Python Advanced Beat Detector',
                'status': 'CAPTURING',
                
                # Advanced frequency bands
                'subBassLevel': min(bands['sub_bass'] * 300, 100.0),
                'lowMidrangeLevel': min(bands['low_midrange'] * 300, 100.0),
                'upperMidrangeLevel': min(bands['upper_midrange'] * 300, 100.0),
                'presenceLevel': min(bands['presence'] * 300, 100.0),
                'brillianceLevel': min(bands['brilliance'] * 300, 100.0),
                
                # Individual beat detection
                'subBassBeat': self.sub_bass_beat,
                'bassBeat': self.bass_beat,
                'lowMidrangeBeat': self.low_midrange_beat,
                'midrangeBeat': self.midrange_beat,
                'upperMidrangeBeat': self.upper_midrange_beat,
                'presenceBeat': self.presence_beat,
                'brillianceBeat': self.brilliance_beat
            }
            
        except Exception as e:
            print(f"❌ Audio processing error: {e}")
            return None

    async def websocket_handler(self, websocket, path):
        """Handle WebSocket connections"""
        print(f"🔌 New WebSocket connection from {websocket.remote_address}")
        self.clients.add(websocket)
        
        try:
            # Send connection confirmation
            await websocket.send(json.dumps({
                'type': 'connection',
                'status': 'connected',
                'message': 'Connected to Python Advanced Beat Detector',
                'source': 'Python FFT Analysis',
                'method': 'PyAudio + NumPy FFT'
            }))
            
            await websocket.wait_closed()
        except Exception as e:
            print(f"❌ WebSocket error: {e}")
        finally:
            self.clients.discard(websocket)
            print(f"🔌 WebSocket connection closed")

    async def broadcast_audio_data(self, data):
        """Broadcast audio data to all connected clients"""
        if self.clients and data:
            message = json.dumps(data)
            disconnected = set()
            
            for client in self.clients:
                try:
                    await client.send(message)
                except Exception:
                    disconnected.add(client)
            
            # Remove disconnected clients
            self.clients -= disconnected

    def audio_callback(self, in_data, frame_count, time_info, status):
        """PyAudio callback for real-time audio processing"""
        if status:
            print(f"⚠️ Audio callback status: {status}")
        
        # Process audio data
        audio_data = self.process_audio_chunk(in_data)
        
        # Schedule broadcast (non-blocking)
        if audio_data:
            asyncio.create_task(self.broadcast_audio_data(audio_data))
        
        return (in_data, pyaudio.paContinue)

    async def start_audio_capture(self):
        """Start audio capture in a separate thread"""
        def audio_thread():
            try:
                # Initialize PyAudio
                p = pyaudio.PyAudio()
                
                print("🎵 Starting Python Advanced Beat Detector...")
                print("🔍 Available audio devices:")
                
                # List audio devices
                for i in range(p.get_device_count()):
                    info = p.get_device_info_by_index(i)
                    if info['maxInputChannels'] > 0:
                        print(f"  📱 {i}: {info['name']}")
                
                # Open audio stream (using default input device)
                stream = p.open(
                    format=pyaudio.paFloat32,
                    channels=1,  # Mono
                    rate=self.sample_rate,
                    input=True,
                    frames_per_buffer=self.chunk_size,
                    stream_callback=self.audio_callback
                )
                
                print("✅ Audio capture started!")
                print("🎵 Listening for beats...")
                
                # Keep the stream active
                stream.start_stream()
                while stream.is_active():
                    time.sleep(0.1)
                    
            except Exception as e:
                print(f"❌ Audio capture error: {e}")
            finally:
                if 'stream' in locals():
                    stream.stop_stream()
                    stream.close()
                if 'p' in locals():
                    p.terminate()
        
        # Start audio capture in background thread
        audio_thread_obj = threading.Thread(target=audio_thread, daemon=True)
        audio_thread_obj.start()

    async def start_server(self):
        """Start the WebSocket server and audio capture"""
        print("🚀 Python Advanced Beat Detector Server")
        print("=" * 50)
        print("🎵 Using original maxemitchell algorithm!")
        print("📊 Real FFT analysis with 7 frequency bands")
        print("🔊 Advanced beat detection per frequency range")
        print()
        
        # Start audio capture
        await self.start_audio_capture()
        
        # Start WebSocket server on port 8766 (different from C# backend)
        server = await websockets.serve(
            self.websocket_handler,
            "localhost",
            8766,
            ping_interval=20,
            ping_timeout=10
        )
        
        print("🌐 WebSocket server started on ws://localhost:8766")
        print("🔗 Connect your frontend to this URL for Python beat detection")
        print("⚡ Server is running... Press Ctrl+C to stop")
        
        await server.wait_closed()

if __name__ == "__main__":
    detector = AdvancedBeatDetector()
    
    try:
        asyncio.run(detector.start_server())
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
