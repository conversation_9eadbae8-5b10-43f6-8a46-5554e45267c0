{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\UI Designolddd\\\\jarvis-ui\\\\src\\\\components\\\\ui\\\\MinimizedJarvis.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MinimizedJarvis = ({\n  onExpand,\n  isProcessing = false\n}) => {\n  _s();\n  var _stateConfig$state3;\n  const [state, setState] = useState('rest');\n  const [isVisible, setIsVisible] = useState(true);\n  const [position, setPosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [showControls, setShowControls] = useState(false);\n\n  // ========================================\n  // JARVIS ACTIVITY MEDIA CONFIGURATION\n  // ========================================\n  //\n  // TO CHANGE ACTIVITY IMAGES/VIDEOS IN THE ICON SPHERE:\n  //\n  // 1. Replace the 'media' filename with your desired file\n  // 2. Place your media files in the 'public/assets/' folder\n  // 3. Supported formats: .gif, .mp4, .webm\n  // 4. You can also change the colors and text for each state\n  //\n  // Current media files used:\n  // - startup.gif    (System initialization animation)\n  // - rest.mp4       (Idle/standby state - main resting animation)\n  // - listening.gif  (When JARVIS is listening for input)\n  // - thinking.gif   (When JARVIS is processing/analyzing)\n  // - speaking.gif   (When JARVIS is responding/speaking)\n  //\n  // ========================================\n  // ACTIVITY-SPECIFIC POSITIONING & SIZING\n  // ========================================\n  //\n  // Each activity can have different position and size:\n  //\n  // POSITION CONTROLS:\n  // - translateX: Horizontal ('-50%' = center, '-40%' = right, '-60%' = left)\n  // - translateY: Vertical ('-50%' = center, '-60%' = up, '-40%' = down)\n  //\n  // SIZE CONTROLS:\n  // - scale: Size multiplier (1.0 = normal, 1.5 = bigger, 0.8 = smaller)\n  //\n  // EXAMPLES:\n  // - Bigger thinking icon: scale: 1.6, translateY: '-60%'\n  // - Smaller speaking icon: scale: 1.1, translateY: '-45%'\n  // - Move listening right: translateX: '-40%'\n  //\n  // ========================================\n\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      // CHANGE: Replace with your startup animation file\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      // CHANGE: Startup glow color (cyan)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,\n      // CHANGE: Size multiplier (1.0 = normal, 1.5 = 50% bigger)\n      translateX: '-50%',\n      // CHANGE: Horizontal position (-50% = centered)\n      translateY: '-50%' // CHANGE: Vertical position (-50% = centered, -60% = higher up)\n    },\n    rest: {\n      media: 'rest.mp4',\n      // CHANGE: Replace with your idle/resting animation file\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      // CHANGE: Rest glow color (cyan)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1.1,\n      // CHANGE: Size multiplier\n      translateX: '-45%',\n      // CHANGE: Horizontal position\n      translateY: '-45%' // CHANGE: Vertical position\n    },\n    listening: {\n      media: 'listening.gif',\n      // CHANGE: Replace with your listening animation file\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      // CHANGE: Listening glow color (pink)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,\n      // CHANGE: Size multiplier (slightly bigger for listening)\n      translateX: '-50%',\n      // CHANGE: Horizontal position\n      translateY: '-50%' // CHANGE: Vertical position (slightly higher)\n    },\n    thinking: {\n      media: 'thinking.gif',\n      // CHANGE: Replace with your thinking/processing animation file\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      // CHANGE: Thinking glow color (orange)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1.1,\n      // CHANGE: Size multiplier (bigger for emphasis)\n      translateX: '-45%',\n      // CHANGE: Horizontal position\n      translateY: '-48%' // CHANGE: Vertical position (moved up to center better)\n    },\n    speaking: {\n      media: 'speaking.gif',\n      // CHANGE: Replace with your speaking/responding animation file\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      // CHANGE: Speaking glow color (green)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,\n      // CHANGE: Size multiplier (medium size)\n      translateX: '-50%',\n      // CHANGE: Horizontal position\n      translateY: '-50%' // CHANGE: Vertical position (slightly lower)\n    }\n  };\n\n  // Enhanced magnetized snap to optimized positions\n  const snapToCorner = (x, y) => {\n    const windowWidth = window.innerWidth;\n    const windowHeight = window.innerHeight;\n    const componentWidth = 280;\n    const componentHeight = 360;\n\n    // Optimized margins for different screen areas\n    const topMargin = 12; // Close to top edge\n    const sideMargin = 12; // Close to side edges\n    const bottomMargin = 12; // Close to bottom edge\n    const headerOffset = 80; // Below typical headers/navbars\n    const chatInputOffset = 100; // Above chat input areas\n\n    // Define magnetized snap zones with priorities\n    const snapZones = [\n    // Top corners (below header area)\n    {\n      x: windowWidth - componentWidth - sideMargin,\n      y: headerOffset,\n      corner: 'top-right',\n      priority: 1,\n      magnetRadius: 150\n    }, {\n      x: sideMargin,\n      y: headerOffset,\n      corner: 'top-left',\n      priority: 2,\n      magnetRadius: 150\n    },\n    // Middle right/left (vertical center)\n    {\n      x: windowWidth - componentWidth - sideMargin,\n      y: (windowHeight - componentHeight) / 2,\n      corner: 'middle-right',\n      priority: 3,\n      magnetRadius: 120\n    }, {\n      x: sideMargin,\n      y: (windowHeight - componentHeight) / 2,\n      corner: 'middle-left',\n      priority: 4,\n      magnetRadius: 120\n    },\n    // Bottom corners (above chat input)\n    {\n      x: windowWidth - componentWidth - sideMargin,\n      y: windowHeight - componentHeight - chatInputOffset,\n      corner: 'bottom-right',\n      priority: 5,\n      magnetRadius: 130\n    }, {\n      x: sideMargin,\n      y: windowHeight - componentHeight - chatInputOffset,\n      corner: 'bottom-left',\n      priority: 6,\n      magnetRadius: 130\n    },\n    // Extreme corners (very edge positions)\n    {\n      x: windowWidth - componentWidth - topMargin,\n      y: topMargin,\n      corner: 'extreme-top-right',\n      priority: 7,\n      magnetRadius: 100\n    }, {\n      x: windowWidth - componentWidth - sideMargin,\n      y: windowHeight - componentHeight - bottomMargin,\n      corner: 'extreme-bottom-right',\n      priority: 8,\n      magnetRadius: 100\n    }];\n\n    // Find the best snap position with magnetism\n    let bestSnap = snapZones[0];\n    let minWeightedDistance = Infinity;\n    snapZones.forEach(zone => {\n      const distance = Math.sqrt(Math.pow(x - zone.x, 2) + Math.pow(y - zone.y, 2));\n\n      // Apply magnetism - closer zones within magnet radius get priority boost\n      const isInMagnetZone = distance <= zone.magnetRadius;\n      const priorityWeight = zone.priority * 0.1; // Lower priority number = better\n      const magnetBoost = isInMagnetZone ? 0.5 : 1; // 50% boost if in magnet zone\n\n      const weightedDistance = distance * magnetBoost + priorityWeight;\n      if (weightedDistance < minWeightedDistance) {\n        minWeightedDistance = weightedDistance;\n        bestSnap = zone;\n      }\n    });\n    return {\n      x: bestSnap.x,\n      y: bestSnap.y,\n      corner: bestSnap.corner\n    };\n  };\n\n  // Handle drag events\n  const handleDragStart = () => {\n    setIsDragging(true);\n  };\n  const handleDrag = (event, info) => {\n    // Keep drag functionality without visual preview\n    // The snap will happen on drag end\n  };\n  const handleDragEnd = (event, info) => {\n    setIsDragging(false);\n    const newPos = snapToCorner(info.point.x - 140, info.point.y - 180);\n\n    // Smooth animation to magnetized position\n    setTimeout(() => {\n      setPosition(newPos);\n    }, 50);\n  };\n\n  // Double-tap functionality\n  const [tapCount, setTapCount] = useState(0);\n  const [tapTimer, setTapTimer] = useState(null);\n  const handleDoubleTap = () => {\n    setTapCount(prev => prev + 1);\n    if (tapTimer) {\n      clearTimeout(tapTimer);\n    }\n    const timer = setTimeout(() => {\n      if (tapCount + 1 >= 2) {\n        // Double tap detected - show controls\n        setShowControls(true);\n\n        // Auto-hide controls after 3 seconds\n        setTimeout(() => {\n          setShowControls(false);\n        }, 3000);\n      }\n      setTapCount(0);\n    }, 300); // 300ms window for double tap\n\n    setTapTimer(timer);\n  };\n\n  // Simulate JARVIS states based on processing\n  useEffect(() => {\n    if (isProcessing) {\n      setState('thinking');\n      const timer = setTimeout(() => {\n        setState('speaking');\n        setTimeout(() => setState('rest'), 2000);\n      }, 1500);\n      return () => clearTimeout(timer);\n    } else {\n      setState('rest');\n    }\n  }, [isProcessing]);\n\n  // Auto-cycle through states for demo\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (!isProcessing) {\n        setState(prev => {\n          switch (prev) {\n            case 'rest':\n              return 'listening';\n            case 'listening':\n              return 'thinking';\n            case 'thinking':\n              return 'speaking';\n            case 'speaking':\n              return 'rest';\n            default:\n              return 'rest';\n          }\n        });\n      }\n    }, 3000);\n    return () => clearInterval(interval);\n  }, [isProcessing]);\n  const getStateColor = () => {\n    var _stateConfig$state;\n    return ((_stateConfig$state = stateConfig[state]) === null || _stateConfig$state === void 0 ? void 0 : _stateConfig$state.color) || '#00eeff';\n  };\n  const getStateText = () => {\n    var _stateConfig$state2;\n    return ((_stateConfig$state2 = stateConfig[state]) === null || _stateConfig$state2 === void 0 ? void 0 : _stateConfig$state2.title) || 'STANDBY MODE';\n  };\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      drag: true,\n      dragMomentum: false,\n      dragElastic: 0.1,\n      dragConstraints: {\n        left: 0,\n        right: window.innerWidth - 280,\n        top: 0,\n        bottom: window.innerHeight - 360\n      },\n      onDragStart: handleDragStart,\n      onDrag: handleDrag,\n      onDragEnd: handleDragEnd,\n      onClick: handleDoubleTap,\n      initial: {\n        scale: 0,\n        opacity: 0,\n        x: window.innerWidth - 196,\n        y: 76\n      } // Start at top-right, lower position\n      ,\n      animate: {\n        scale: 1,\n        opacity: 1,\n        x: position.x || window.innerWidth - 296,\n        y: position.y || 76,\n        transition: {\n          type: \"spring\",\n          damping: 25,\n          stiffness: 200,\n          duration: 0.6\n        }\n      },\n      exit: {\n        scale: 0,\n        opacity: 0\n      },\n      className: `fixed z-30 select-none ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`,\n      style: {\n        width: '280px',\n        height: '360px'\n      },\n      whileHover: {\n        scale: 1.02\n      },\n      whileDrag: {\n        scale: 1.05,\n        zIndex: 50\n      },\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: showControls && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(motion.button, {\n            initial: {\n              scale: 0,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            exit: {\n              scale: 0,\n              opacity: 0\n            },\n            transition: {\n              type: \"spring\",\n              damping: 15,\n              stiffness: 300\n            },\n            onClick: () => setIsVisible(false),\n            className: \"absolute -top-2 -right-2 w-10 h-10 rounded-full bg-red-500/30 hover:bg-red-500/50 border-2 border-red-500/70 flex items-center justify-center text-red-300 hover:text-red-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-red-500/30\",\n            onMouseDown: e => e.stopPropagation() // Prevent drag when clicking button\n            ,\n            style: {\n              boxShadow: '0 0 20px rgba(239, 68, 68, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            initial: {\n              scale: 0,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            exit: {\n              scale: 0,\n              opacity: 0\n            },\n            transition: {\n              type: \"spring\",\n              damping: 15,\n              stiffness: 300,\n              delay: 0.1\n            },\n            onClick: onExpand,\n            className: \"absolute -top-2 -left-2 w-10 h-10 rounded-full bg-cyan-500/30 hover:bg-cyan-500/50 border-2 border-cyan-500/70 flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-cyan-500/30\",\n            title: \"Expand JARVIS\",\n            onMouseDown: e => e.stopPropagation() // Prevent drag when clicking button\n            ,\n            style: {\n              boxShadow: '0 0 20px rgba(6, 182, 212, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'\n            },\n            children: \"\\u2197\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-full\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"relative w-56 h-56 rounded-full overflow-hidden mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            mode: \"wait\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.95\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              exit: {\n                opacity: 0,\n                scale: 1.05\n              },\n              transition: {\n                duration: 0.6,\n                ease: [0.4, 0, 0.2, 1],\n                opacity: {\n                  duration: 0.4\n                }\n              },\n              className: \"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden\",\n              children: stateConfig[state].media.endsWith('.mp4') ? /*#__PURE__*/_jsxDEV(\"video\", {\n                src: `/assets/${stateConfig[state].media}`,\n                autoPlay: true,\n                muted: true,\n                loop: true,\n                playsInline: true,\n                className: \"rounded-full\",\n                style: {\n                  clipPath: 'circle(50% at 50% 50%)',\n                  width: '120%',\n                  height: '120%',\n                  objectFit: 'cover',\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,\n                  pointerEvents: 'none',\n                  userSelect: 'none',\n                  transition: 'all 0.3s ease-in-out'\n                },\n                onError: e => {\n                  // Fallback to SVG if video fails to load\n                  e.target.style.display = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `/assets/${stateConfig[state].media}`,\n                alt: state,\n                draggable: false,\n                className: \"rounded-full\",\n                style: {\n                  clipPath: 'circle(50% at 50% 50%)',\n                  width: '120%',\n                  height: '120%',\n                  objectFit: 'cover',\n                  imageRendering: 'auto',\n                  filter: 'contrast(1.1) brightness(1.05)',\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,\n                  transition: 'all 0.3s ease-in-out',\n                  pointerEvents: 'none',\n                  userSelect: 'none'\n                },\n                onError: e => {\n                  // Fallback to SVG if GIF fails to load\n                  e.target.style.display = 'none';\n                  e.target.parentElement.innerHTML = `\n                      <div class=\"w-full h-full rounded-full border-2 flex items-center justify-center\"\n                           style=\"border-color: ${getStateColor()}; box-shadow: 0 0 20px ${getStateColor()}40;\">\n                        <svg viewBox=\"0 0 100 100\" class=\"w-16 h-16\">\n                          <circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"${getStateColor()}\" stroke-width=\"2\"/>\n                          <circle cx=\"50\" cy=\"50\" r=\"20\" fill=\"none\" stroke=\"${getStateColor()}\" stroke-width=\"1.5\" opacity=\"0.7\"/>\n                          <circle cx=\"50\" cy=\"50\" r=\"8\" fill=\"${getStateColor()}\"/>\n                          <path d=\"M30 50 L70 50 M50 30 L50 70\" stroke=\"${getStateColor()}\" stroke-width=\"1.5\" opacity=\"0.8\"/>\n                        </svg>\n                      </div>\n                    `;\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this)\n            }, state, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute inset-0 rounded-full pointer-events-none border-2\",\n            style: {\n              borderColor: getStateColor(),\n              animation: `blinkingCircle 2s ease-in-out infinite`,\n              boxShadow: `0 0 20px ${getStateColor()}60`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-base font-bold transition-colors duration-300 mb-1\",\n            style: {\n              color: getStateColor()\n            },\n            children: getStateText()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-300 font-medium tracking-wider\",\n            children: \"JARVIS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400\",\n            children: (_stateConfig$state3 = stateConfig[state]) === null || _stateConfig$state3 === void 0 ? void 0 : _stateConfig$state3.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n};\n_s(MinimizedJarvis, \"WFFLxFTlbqqXI1tWmgtJlRLJZuc=\");\n_c = MinimizedJarvis;\nexport default MinimizedJarvis;\nvar _c;\n$RefreshReg$(_c, \"MinimizedJarvis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Mini<PERSON><PERSON><PERSON><PERSON>", "onExpand", "isProcessing", "_s", "_stateConfig$state3", "state", "setState", "isVisible", "setIsVisible", "position", "setPosition", "x", "y", "isDragging", "setIsDragging", "showControls", "setShowControls", "stateConfig", "startup", "media", "title", "description", "color", "loop", "scale", "translateX", "translateY", "rest", "listening", "thinking", "speaking", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "windowWidth", "window", "innerWidth", "windowHeight", "innerHeight", "componentWidth", "componentHeight", "<PERSON><PERSON><PERSON><PERSON>", "sideMargin", "bottom<PERSON>argin", "headerOffset", "chatInputOffset", "snapZones", "corner", "priority", "magnetRadius", "bestSnap", "minWeightedDistance", "Infinity", "for<PERSON>ach", "zone", "distance", "Math", "sqrt", "pow", "isInMagnetZone", "priorityWeight", "magnetBoost", "weightedDistance", "handleDragStart", "handleDrag", "event", "info", "handleDragEnd", "newPos", "point", "setTimeout", "tapCount", "setTapCount", "tapTimer", "setTapTimer", "handleDoubleTap", "prev", "clearTimeout", "timer", "interval", "setInterval", "clearInterval", "getStateColor", "_stateConfig$state", "getStateText", "_stateConfig$state2", "children", "div", "drag", "dragMomentum", "dragElastic", "dragConstraints", "left", "right", "top", "bottom", "onDragStart", "onDrag", "onDragEnd", "onClick", "initial", "opacity", "animate", "transition", "type", "damping", "stiffness", "duration", "exit", "className", "style", "width", "height", "whileHover", "whileDrag", "zIndex", "button", "onMouseDown", "e", "stopPropagation", "boxShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "mode", "ease", "endsWith", "src", "autoPlay", "muted", "playsInline", "clipPath", "objectFit", "transform", "pointerEvents", "userSelect", "onError", "target", "display", "alt", "draggable", "imageRendering", "filter", "parentElement", "innerHTML", "borderColor", "animation", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/UI Designolddd/jarvis-ui/src/components/ui/MinimizedJarvis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst MinimizedJarvis = ({ onExpand, isProcessing = false }) => {\n  const [state, setState] = useState('rest');\n  const [isVisible, setIsVisible] = useState(true);\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [showControls, setShowControls] = useState(false);\n\n\n\n  // ========================================\n  // JARVIS ACTIVITY MEDIA CONFIGURATION\n  // ========================================\n  //\n  // TO CHANGE ACTIVITY IMAGES/VIDEOS IN THE ICON SPHERE:\n  //\n  // 1. Replace the 'media' filename with your desired file\n  // 2. Place your media files in the 'public/assets/' folder\n  // 3. Supported formats: .gif, .mp4, .webm\n  // 4. You can also change the colors and text for each state\n  //\n  // Current media files used:\n  // - startup.gif    (System initialization animation)\n  // - rest.mp4       (Idle/standby state - main resting animation)\n  // - listening.gif  (When JARVIS is listening for input)\n  // - thinking.gif   (When JARVIS is processing/analyzing)\n  // - speaking.gif   (When JARVIS is responding/speaking)\n  //\n  // ========================================\n  // ACTIVITY-SPECIFIC POSITIONING & SIZING\n  // ========================================\n  //\n  // Each activity can have different position and size:\n  //\n  // POSITION CONTROLS:\n  // - translateX: Horizontal ('-50%' = center, '-40%' = right, '-60%' = left)\n  // - translateY: Vertical ('-50%' = center, '-60%' = up, '-40%' = down)\n  //\n  // SIZE CONTROLS:\n  // - scale: Size multiplier (1.0 = normal, 1.5 = bigger, 0.8 = smaller)\n  //\n  // EXAMPLES:\n  // - Bigger thinking icon: scale: 1.6, translateY: '-60%'\n  // - Smaller speaking icon: scale: 1.1, translateY: '-45%'\n  // - Move listening right: translateX: '-40%'\n  //\n  // ========================================\n\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',        // CHANGE: Replace with your startup animation file\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',            // CHANGE: Startup glow color (cyan)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,                  // CHANGE: Size multiplier (1.0 = normal, 1.5 = 50% bigger)\n      translateX: '-50%',          // CHANGE: Horizontal position (-50% = centered)\n      translateY: '-50%'           // CHANGE: Vertical position (-50% = centered, -60% = higher up)\n    },\n    rest: {\n      media: 'rest.mp4',           // CHANGE: Replace with your idle/resting animation file\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',            // CHANGE: Rest glow color (cyan)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1.1,                  // CHANGE: Size multiplier\n      translateX: '-45%',          // CHANGE: Horizontal position\n      translateY: '-45%'           // CHANGE: Vertical position\n    },\n    listening: {\n      media: 'listening.gif',      // CHANGE: Replace with your listening animation file\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',            // CHANGE: Listening glow color (pink)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,                  // CHANGE: Size multiplier (slightly bigger for listening)\n      translateX: '-50%',          // CHANGE: Horizontal position\n      translateY: '-50%'           // CHANGE: Vertical position (slightly higher)\n    },\n    thinking: {\n      media: 'thinking.gif',       // CHANGE: Replace with your thinking/processing animation file\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',            // CHANGE: Thinking glow color (orange)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1.1,                  // CHANGE: Size multiplier (bigger for emphasis)\n      translateX: '-45%',          // CHANGE: Horizontal position\n      translateY: '-48%'           // CHANGE: Vertical position (moved up to center better)\n    },\n    speaking: {\n      media: 'speaking.gif',       // CHANGE: Replace with your speaking/responding animation file\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',            // CHANGE: Speaking glow color (green)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,                 // CHANGE: Size multiplier (medium size)\n      translateX: '-50%',          // CHANGE: Horizontal position\n      translateY: '-50%'           // CHANGE: Vertical position (slightly lower)\n    }\n  };\n\n\n  // Enhanced magnetized snap to optimized positions\n  const snapToCorner = (x, y) => {\n    const windowWidth = window.innerWidth;\n    const windowHeight = window.innerHeight;\n    const componentWidth = 280;\n    const componentHeight = 360;\n\n    // Optimized margins for different screen areas\n    const topMargin = 12;           // Close to top edge\n    const sideMargin = 12;          // Close to side edges\n    const bottomMargin = 12;        // Close to bottom edge\n    const headerOffset = 80;       // Below typical headers/navbars\n    const chatInputOffset = 100;   // Above chat input areas\n\n    // Define magnetized snap zones with priorities\n    const snapZones = [\n      // Top corners (below header area)\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: headerOffset,\n        corner: 'top-right',\n        priority: 1,\n        magnetRadius: 150\n      },\n      {\n        x: sideMargin,\n        y: headerOffset,\n        corner: 'top-left',\n        priority: 2,\n        magnetRadius: 150\n      },\n\n      // Middle right/left (vertical center)\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: (windowHeight - componentHeight) / 2,\n        corner: 'middle-right',\n        priority: 3,\n        magnetRadius: 120\n      },\n      {\n        x: sideMargin,\n        y: (windowHeight - componentHeight) / 2,\n        corner: 'middle-left',\n        priority: 4,\n        magnetRadius: 120\n      },\n\n      // Bottom corners (above chat input)\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: windowHeight - componentHeight - chatInputOffset,\n        corner: 'bottom-right',\n        priority: 5,\n        magnetRadius: 130\n      },\n      {\n        x: sideMargin,\n        y: windowHeight - componentHeight - chatInputOffset,\n        corner: 'bottom-left',\n        priority: 6,\n        magnetRadius: 130\n      },\n\n      // Extreme corners (very edge positions)\n      {\n        x: windowWidth - componentWidth - topMargin,\n        y: topMargin,\n        corner: 'extreme-top-right',\n        priority: 7,\n        magnetRadius: 100\n      },\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: windowHeight - componentHeight - bottomMargin,\n        corner: 'extreme-bottom-right',\n        priority: 8,\n        magnetRadius: 100\n      }\n    ];\n\n    // Find the best snap position with magnetism\n    let bestSnap = snapZones[0];\n    let minWeightedDistance = Infinity;\n\n    snapZones.forEach(zone => {\n      const distance = Math.sqrt(Math.pow(x - zone.x, 2) + Math.pow(y - zone.y, 2));\n\n      // Apply magnetism - closer zones within magnet radius get priority boost\n      const isInMagnetZone = distance <= zone.magnetRadius;\n      const priorityWeight = zone.priority * 0.1; // Lower priority number = better\n      const magnetBoost = isInMagnetZone ? 0.5 : 1; // 50% boost if in magnet zone\n\n      const weightedDistance = (distance * magnetBoost) + priorityWeight;\n\n      if (weightedDistance < minWeightedDistance) {\n        minWeightedDistance = weightedDistance;\n        bestSnap = zone;\n      }\n    });\n\n    return { x: bestSnap.x, y: bestSnap.y, corner: bestSnap.corner };\n  };\n\n  // Handle drag events\n  const handleDragStart = () => {\n    setIsDragging(true);\n  };\n\n  const handleDrag = (event, info) => {\n    // Keep drag functionality without visual preview\n    // The snap will happen on drag end\n  };\n\n  const handleDragEnd = (event, info) => {\n    setIsDragging(false);\n\n    const newPos = snapToCorner(info.point.x - 140, info.point.y - 180);\n\n    // Smooth animation to magnetized position\n    setTimeout(() => {\n      setPosition(newPos);\n    }, 50);\n  };\n\n  // Double-tap functionality\n  const [tapCount, setTapCount] = useState(0);\n  const [tapTimer, setTapTimer] = useState(null);\n\n  const handleDoubleTap = () => {\n    setTapCount(prev => prev + 1);\n\n    if (tapTimer) {\n      clearTimeout(tapTimer);\n    }\n\n    const timer = setTimeout(() => {\n      if (tapCount + 1 >= 2) {\n        // Double tap detected - show controls\n        setShowControls(true);\n\n        // Auto-hide controls after 3 seconds\n        setTimeout(() => {\n          setShowControls(false);\n        }, 3000);\n      }\n      setTapCount(0);\n    }, 300); // 300ms window for double tap\n\n    setTapTimer(timer);\n  };\n\n  // Simulate JARVIS states based on processing\n  useEffect(() => {\n    if (isProcessing) {\n      setState('thinking');\n      const timer = setTimeout(() => {\n        setState('speaking');\n        setTimeout(() => setState('rest'), 2000);\n      }, 1500);\n      return () => clearTimeout(timer);\n    } else {\n      setState('rest');\n    }\n  }, [isProcessing]);\n\n  // Auto-cycle through states for demo\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (!isProcessing) {\n        setState(prev => {\n          switch (prev) {\n            case 'rest': return 'listening';\n            case 'listening': return 'thinking';\n            case 'thinking': return 'speaking';\n            case 'speaking': return 'rest';\n            default: return 'rest';\n          }\n        });\n      }\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, [isProcessing]);\n\n  const getStateColor = () => {\n    return stateConfig[state]?.color || '#00eeff';\n  };\n\n  const getStateText = () => {\n    return stateConfig[state]?.title || 'STANDBY MODE';\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <>\n\n\n      {/* Main Draggable Window */}\n    <motion.div\n      drag\n      dragMomentum={false}\n      dragElastic={0.1}\n      dragConstraints={{\n        left: 0,\n        right: window.innerWidth - 280,\n        top: 0,\n        bottom: window.innerHeight - 360\n      }}\n      onDragStart={handleDragStart}\n      onDrag={handleDrag}\n      onDragEnd={handleDragEnd}\n      onClick={handleDoubleTap}\n      initial={{ scale: 0, opacity: 0, x: window.innerWidth - 196, y: 76 }} // Start at top-right, lower position\n      animate={{\n        scale: 1,\n        opacity: 1,\n        x: position.x || window.innerWidth - 296,\n        y: position.y || 76,\n        transition: {\n          type: \"spring\",\n          damping: 25,\n          stiffness: 200,\n          duration: 0.6\n        }\n      }}\n      exit={{ scale: 0, opacity: 0 }}\n      className={`fixed z-30 select-none ${\n        isDragging ? 'cursor-grabbing' : 'cursor-grab'\n      }`}\n      style={{ width: '280px', height: '360px' }}\n      whileHover={{ scale: 1.02 }}\n      whileDrag={{ scale: 1.05, zIndex: 50 }}\n    >\n\n\n      {/* Control Buttons - Only visible when double-tapped */}\n      <AnimatePresence>\n        {showControls && (\n          <>\n            {/* Close/Minimize Button */}\n            <motion.button\n              initial={{ scale: 0, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0, opacity: 0 }}\n              transition={{ type: \"spring\", damping: 15, stiffness: 300 }}\n              onClick={() => setIsVisible(false)}\n              className=\"absolute -top-2 -right-2 w-10 h-10 rounded-full bg-red-500/30 hover:bg-red-500/50 border-2 border-red-500/70 flex items-center justify-center text-red-300 hover:text-red-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-red-500/30\"\n              onMouseDown={(e) => e.stopPropagation()} // Prevent drag when clicking button\n              style={{\n                boxShadow: '0 0 20px rgba(239, 68, 68, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'\n              }}\n            >\n              ×\n            </motion.button>\n\n            {/* Expand Button */}\n            <motion.button\n              initial={{ scale: 0, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0, opacity: 0 }}\n              transition={{ type: \"spring\", damping: 15, stiffness: 300, delay: 0.1 }}\n              onClick={onExpand}\n              className=\"absolute -top-2 -left-2 w-10 h-10 rounded-full bg-cyan-500/30 hover:bg-cyan-500/50 border-2 border-cyan-500/70 flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-cyan-500/30\"\n              title=\"Expand JARVIS\"\n              onMouseDown={(e) => e.stopPropagation()} // Prevent drag when clicking button\n              style={{\n                boxShadow: '0 0 20px rgba(6, 182, 212, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'\n              }}\n            >\n              ↗\n            </motion.button>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* JARVIS Media Display - Larger and more circular with Audio Reactivity */}\n      <div className=\"flex flex-col items-center justify-center h-full\">\n        {/* Media Container */}\n        <motion.div\n          className=\"relative w-56 h-56 rounded-full overflow-hidden mb-3\"\n        >\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={state}\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 1.05 }}\n              transition={{\n                duration: 0.6,\n                ease: [0.4, 0, 0.2, 1],\n                opacity: { duration: 0.4 }\n              }}\n              className=\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden\"\n            >\n              {stateConfig[state].media.endsWith('.mp4') ? (\n                <video\n                  src={`/assets/${stateConfig[state].media}`}\n                  autoPlay\n                  muted\n                  loop\n                  playsInline\n                  className=\"rounded-full\"\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    width: '120%',\n                    height: '120%',\n                    objectFit: 'cover',\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,\n                    pointerEvents: 'none',\n                    userSelect: 'none',\n                    transition: 'all 0.3s ease-in-out'\n                  }}\n                  onError={(e) => {\n                    // Fallback to SVG if video fails to load\n                    e.target.style.display = 'none';\n                  }}\n                />\n              ) : (\n                <img\n                  src={`/assets/${stateConfig[state].media}`}\n                  alt={state}\n                  draggable={false}\n                  className=\"rounded-full\"\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    width: '120%',\n                    height: '120%',\n                    objectFit: 'cover',\n                    imageRendering: 'auto',\n                    filter: 'contrast(1.1) brightness(1.05)',\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,\n                    transition: 'all 0.3s ease-in-out',\n                    pointerEvents: 'none',\n                    userSelect: 'none'\n                  }}\n                  onError={(e) => {\n                    // Fallback to SVG if GIF fails to load\n                    e.target.style.display = 'none';\n                    e.target.parentElement.innerHTML = `\n                      <div class=\"w-full h-full rounded-full border-2 flex items-center justify-center\"\n                           style=\"border-color: ${getStateColor()}; box-shadow: 0 0 20px ${getStateColor()}40;\">\n                        <svg viewBox=\"0 0 100 100\" class=\"w-16 h-16\">\n                          <circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"${getStateColor()}\" stroke-width=\"2\"/>\n                          <circle cx=\"50\" cy=\"50\" r=\"20\" fill=\"none\" stroke=\"${getStateColor()}\" stroke-width=\"1.5\" opacity=\"0.7\"/>\n                          <circle cx=\"50\" cy=\"50\" r=\"8\" fill=\"${getStateColor()}\"/>\n                          <path d=\"M30 50 L70 50 M50 30 L50 70\" stroke=\"${getStateColor()}\" stroke-width=\"1.5\" opacity=\"0.8\"/>\n                        </svg>\n                      </div>\n                    `;\n                  }}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Blinking Circle Animation */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full pointer-events-none border-2\"\n            style={{\n              borderColor: getStateColor(),\n              animation: `blinkingCircle 2s ease-in-out infinite`,\n              boxShadow: `0 0 20px ${getStateColor()}60`\n            }}\n          />\n        </motion.div>\n\n        {/* Status Text */}\n        <div className=\"text-center\">\n          <div\n            className=\"text-base font-bold transition-colors duration-300 mb-1\"\n            style={{\n              color: getStateColor()\n            }}\n          >\n            {getStateText()}\n          </div>\n          <div className=\"text-sm text-gray-300 font-medium tracking-wider\">\n            JARVIS\n          </div>\n          <div className=\"text-xs text-gray-400\">\n            {stateConfig[state]?.description}\n          </div>\n        </div>\n      </div>\n\n\n    </motion.div>\n    </>\n  );\n};\n\nexport default MinimizedJarvis;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EAC9D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,MAAM,CAAC;EAC1C,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IAAEmB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EACxD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAIvD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMyB,WAAW,GAAG;IAClBC,OAAO,EAAE;MACPC,KAAK,EAAE,aAAa;MAAS;MAC7BC,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,qBAAqB;MAClCC,KAAK,EAAE,SAAS;MAAa;MAC7BC,IAAI,EAAE,IAAI;MACV;MACAC,KAAK,EAAE,CAAC;MAAmB;MAC3BC,UAAU,EAAE,MAAM;MAAW;MAC7BC,UAAU,EAAE,MAAM,CAAW;IAC/B,CAAC;IACDC,IAAI,EAAE;MACJR,KAAK,EAAE,UAAU;MAAY;MAC7BC,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,SAAS;MAAa;MAC7BC,IAAI,EAAE,IAAI;MACV;MACAC,KAAK,EAAE,GAAG;MAAmB;MAC7BC,UAAU,EAAE,MAAM;MAAW;MAC7BC,UAAU,EAAE,MAAM,CAAW;IAC/B,CAAC;IACDE,SAAS,EAAE;MACTT,KAAK,EAAE,eAAe;MAAO;MAC7BC,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,wBAAwB;MACrCC,KAAK,EAAE,SAAS;MAAa;MAC7BC,IAAI,EAAE,IAAI;MACV;MACAC,KAAK,EAAE,CAAC;MAAmB;MAC3BC,UAAU,EAAE,MAAM;MAAW;MAC7BC,UAAU,EAAE,MAAM,CAAW;IAC/B,CAAC;IACDG,QAAQ,EAAE;MACRV,KAAK,EAAE,cAAc;MAAQ;MAC7BC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAAa;MAC7BC,IAAI,EAAE,IAAI;MACV;MACAC,KAAK,EAAE,GAAG;MAAmB;MAC7BC,UAAU,EAAE,MAAM;MAAW;MAC7BC,UAAU,EAAE,MAAM,CAAW;IAC/B,CAAC;IACDI,QAAQ,EAAE;MACRX,KAAK,EAAE,cAAc;MAAQ;MAC7BC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAAa;MAC7BC,IAAI,EAAE,IAAI;MACV;MACAC,KAAK,EAAE,CAAC;MAAkB;MAC1BC,UAAU,EAAE,MAAM;MAAW;MAC7BC,UAAU,EAAE,MAAM,CAAW;IAC/B;EACF,CAAC;;EAGD;EACA,MAAMK,YAAY,GAAGA,CAACpB,CAAC,EAAEC,CAAC,KAAK;IAC7B,MAAMoB,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,MAAMC,YAAY,GAAGF,MAAM,CAACG,WAAW;IACvC,MAAMC,cAAc,GAAG,GAAG;IAC1B,MAAMC,eAAe,GAAG,GAAG;;IAE3B;IACA,MAAMC,SAAS,GAAG,EAAE,CAAC,CAAW;IAChC,MAAMC,UAAU,GAAG,EAAE,CAAC,CAAU;IAChC,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAQ;IAChC,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAO;IAC/B,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAG;;IAE/B;IACA,MAAMC,SAAS,GAAG;IAChB;IACA;MACEjC,CAAC,EAAEqB,WAAW,GAAGK,cAAc,GAAGG,UAAU;MAC5C5B,CAAC,EAAE8B,YAAY;MACfG,MAAM,EAAE,WAAW;MACnBC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC,EACD;MACEpC,CAAC,EAAE6B,UAAU;MACb5B,CAAC,EAAE8B,YAAY;MACfG,MAAM,EAAE,UAAU;MAClBC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC;IAED;IACA;MACEpC,CAAC,EAAEqB,WAAW,GAAGK,cAAc,GAAGG,UAAU;MAC5C5B,CAAC,EAAE,CAACuB,YAAY,GAAGG,eAAe,IAAI,CAAC;MACvCO,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC,EACD;MACEpC,CAAC,EAAE6B,UAAU;MACb5B,CAAC,EAAE,CAACuB,YAAY,GAAGG,eAAe,IAAI,CAAC;MACvCO,MAAM,EAAE,aAAa;MACrBC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC;IAED;IACA;MACEpC,CAAC,EAAEqB,WAAW,GAAGK,cAAc,GAAGG,UAAU;MAC5C5B,CAAC,EAAEuB,YAAY,GAAGG,eAAe,GAAGK,eAAe;MACnDE,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC,EACD;MACEpC,CAAC,EAAE6B,UAAU;MACb5B,CAAC,EAAEuB,YAAY,GAAGG,eAAe,GAAGK,eAAe;MACnDE,MAAM,EAAE,aAAa;MACrBC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC;IAED;IACA;MACEpC,CAAC,EAAEqB,WAAW,GAAGK,cAAc,GAAGE,SAAS;MAC3C3B,CAAC,EAAE2B,SAAS;MACZM,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC,EACD;MACEpC,CAAC,EAAEqB,WAAW,GAAGK,cAAc,GAAGG,UAAU;MAC5C5B,CAAC,EAAEuB,YAAY,GAAGG,eAAe,GAAGG,YAAY;MAChDI,MAAM,EAAE,sBAAsB;MAC9BC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB,CAAC,CACF;;IAED;IACA,IAAIC,QAAQ,GAAGJ,SAAS,CAAC,CAAC,CAAC;IAC3B,IAAIK,mBAAmB,GAAGC,QAAQ;IAElCN,SAAS,CAACO,OAAO,CAACC,IAAI,IAAI;MACxB,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC7C,CAAC,GAAGyC,IAAI,CAACzC,CAAC,EAAE,CAAC,CAAC,GAAG2C,IAAI,CAACE,GAAG,CAAC5C,CAAC,GAAGwC,IAAI,CAACxC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE7E;MACA,MAAM6C,cAAc,GAAGJ,QAAQ,IAAID,IAAI,CAACL,YAAY;MACpD,MAAMW,cAAc,GAAGN,IAAI,CAACN,QAAQ,GAAG,GAAG,CAAC,CAAC;MAC5C,MAAMa,WAAW,GAAGF,cAAc,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;;MAE9C,MAAMG,gBAAgB,GAAIP,QAAQ,GAAGM,WAAW,GAAID,cAAc;MAElE,IAAIE,gBAAgB,GAAGX,mBAAmB,EAAE;QAC1CA,mBAAmB,GAAGW,gBAAgB;QACtCZ,QAAQ,GAAGI,IAAI;MACjB;IACF,CAAC,CAAC;IAEF,OAAO;MAAEzC,CAAC,EAAEqC,QAAQ,CAACrC,CAAC;MAAEC,CAAC,EAAEoC,QAAQ,CAACpC,CAAC;MAAEiC,MAAM,EAAEG,QAAQ,CAACH;IAAO,CAAC;EAClE,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAGA,CAAA,KAAM;IAC5B/C,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMgD,UAAU,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;IAClC;IACA;EAAA,CACD;EAED,MAAMC,aAAa,GAAGA,CAACF,KAAK,EAAEC,IAAI,KAAK;IACrClD,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMoD,MAAM,GAAGnC,YAAY,CAACiC,IAAI,CAACG,KAAK,CAACxD,CAAC,GAAG,GAAG,EAAEqD,IAAI,CAACG,KAAK,CAACvD,CAAC,GAAG,GAAG,CAAC;;IAEnE;IACAwD,UAAU,CAAC,MAAM;MACf1D,WAAW,CAACwD,MAAM,CAAC;IACrB,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;;EAED;EACA,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAMiF,eAAe,GAAGA,CAAA,KAAM;IAC5BH,WAAW,CAACI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAE7B,IAAIH,QAAQ,EAAE;MACZI,YAAY,CAACJ,QAAQ,CAAC;IACxB;IAEA,MAAMK,KAAK,GAAGR,UAAU,CAAC,MAAM;MAC7B,IAAIC,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE;QACrB;QACArD,eAAe,CAAC,IAAI,CAAC;;QAErB;QACAoD,UAAU,CAAC,MAAM;UACfpD,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC;MACV;MACAsD,WAAW,CAAC,CAAC,CAAC;IAChB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAETE,WAAW,CAACI,KAAK,CAAC;EACpB,CAAC;;EAED;EACAnF,SAAS,CAAC,MAAM;IACd,IAAIS,YAAY,EAAE;MAChBI,QAAQ,CAAC,UAAU,CAAC;MACpB,MAAMsE,KAAK,GAAGR,UAAU,CAAC,MAAM;QAC7B9D,QAAQ,CAAC,UAAU,CAAC;QACpB8D,UAAU,CAAC,MAAM9D,QAAQ,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMqE,YAAY,CAACC,KAAK,CAAC;IAClC,CAAC,MAAM;MACLtE,QAAQ,CAAC,MAAM,CAAC;IAClB;EACF,CAAC,EAAE,CAACJ,YAAY,CAAC,CAAC;;EAElB;EACAT,SAAS,CAAC,MAAM;IACd,MAAMoF,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAI,CAAC5E,YAAY,EAAE;QACjBI,QAAQ,CAACoE,IAAI,IAAI;UACf,QAAQA,IAAI;YACV,KAAK,MAAM;cAAE,OAAO,WAAW;YAC/B,KAAK,WAAW;cAAE,OAAO,UAAU;YACnC,KAAK,UAAU;cAAE,OAAO,UAAU;YAClC,KAAK,UAAU;cAAE,OAAO,MAAM;YAC9B;cAAS,OAAO,MAAM;UACxB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMK,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC3E,YAAY,CAAC,CAAC;EAElB,MAAM8E,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA;IAC1B,OAAO,EAAAA,kBAAA,GAAAhE,WAAW,CAACZ,KAAK,CAAC,cAAA4E,kBAAA,uBAAlBA,kBAAA,CAAoB3D,KAAK,KAAI,SAAS;EAC/C,CAAC;EAED,MAAM4D,YAAY,GAAGA,CAAA,KAAM;IAAA,IAAAC,mBAAA;IACzB,OAAO,EAAAA,mBAAA,GAAAlE,WAAW,CAACZ,KAAK,CAAC,cAAA8E,mBAAA,uBAAlBA,mBAAA,CAAoB/D,KAAK,KAAI,cAAc;EACpD,CAAC;EAED,IAAI,CAACb,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEV,OAAA,CAAAE,SAAA;IAAAqF,QAAA,eAIAvF,OAAA,CAACH,MAAM,CAAC2F,GAAG;MACTC,IAAI;MACJC,YAAY,EAAE,KAAM;MACpBC,WAAW,EAAE,GAAI;MACjBC,eAAe,EAAE;QACfC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE1D,MAAM,CAACC,UAAU,GAAG,GAAG;QAC9B0D,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE5D,MAAM,CAACG,WAAW,GAAG;MAC/B,CAAE;MACF0D,WAAW,EAAEjC,eAAgB;MAC7BkC,MAAM,EAAEjC,UAAW;MACnBkC,SAAS,EAAE/B,aAAc;MACzBgC,OAAO,EAAExB,eAAgB;MACzByB,OAAO,EAAE;QAAE1E,KAAK,EAAE,CAAC;QAAE2E,OAAO,EAAE,CAAC;QAAExF,CAAC,EAAEsB,MAAM,CAACC,UAAU,GAAG,GAAG;QAAEtB,CAAC,EAAE;MAAG,CAAE,CAAC;MAAA;MACtEwF,OAAO,EAAE;QACP5E,KAAK,EAAE,CAAC;QACR2E,OAAO,EAAE,CAAC;QACVxF,CAAC,EAAEF,QAAQ,CAACE,CAAC,IAAIsB,MAAM,CAACC,UAAU,GAAG,GAAG;QACxCtB,CAAC,EAAEH,QAAQ,CAACG,CAAC,IAAI,EAAE;QACnByF,UAAU,EAAE;UACVC,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;QACZ;MACF,CAAE;MACFC,IAAI,EAAE;QAAElF,KAAK,EAAE,CAAC;QAAE2E,OAAO,EAAE;MAAE,CAAE;MAC/BQ,SAAS,EAAE,0BACT9F,UAAU,GAAG,iBAAiB,GAAG,aAAa,EAC7C;MACH+F,KAAK,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAC3CC,UAAU,EAAE;QAAEvF,KAAK,EAAE;MAAK,CAAE;MAC5BwF,SAAS,EAAE;QAAExF,KAAK,EAAE,IAAI;QAAEyF,MAAM,EAAE;MAAG,CAAE;MAAA7B,QAAA,gBAKvCvF,OAAA,CAACF,eAAe;QAAAyF,QAAA,EACbrE,YAAY,iBACXlB,OAAA,CAAAE,SAAA;UAAAqF,QAAA,gBAEEvF,OAAA,CAACH,MAAM,CAACwH,MAAM;YACZhB,OAAO,EAAE;cAAE1E,KAAK,EAAE,CAAC;cAAE2E,OAAO,EAAE;YAAE,CAAE;YAClCC,OAAO,EAAE;cAAE5E,KAAK,EAAE,CAAC;cAAE2E,OAAO,EAAE;YAAE,CAAE;YAClCO,IAAI,EAAE;cAAElF,KAAK,EAAE,CAAC;cAAE2E,OAAO,EAAE;YAAE,CAAE;YAC/BE,UAAU,EAAE;cAAEC,IAAI,EAAE,QAAQ;cAAEC,OAAO,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAI,CAAE;YAC5DP,OAAO,EAAEA,CAAA,KAAMzF,YAAY,CAAC,KAAK,CAAE;YACnCmG,SAAS,EAAC,oPAAoP;YAC9PQ,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;YAAA;YACzCT,KAAK,EAAE;cACLU,SAAS,EAAE;YACb,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAGhB7H,OAAA,CAACH,MAAM,CAACwH,MAAM;YACZhB,OAAO,EAAE;cAAE1E,KAAK,EAAE,CAAC;cAAE2E,OAAO,EAAE;YAAE,CAAE;YAClCC,OAAO,EAAE;cAAE5E,KAAK,EAAE,CAAC;cAAE2E,OAAO,EAAE;YAAE,CAAE;YAClCO,IAAI,EAAE;cAAElF,KAAK,EAAE,CAAC;cAAE2E,OAAO,EAAE;YAAE,CAAE;YAC/BE,UAAU,EAAE;cAAEC,IAAI,EAAE,QAAQ;cAAEC,OAAO,EAAE,EAAE;cAAEC,SAAS,EAAE,GAAG;cAAEmB,KAAK,EAAE;YAAI,CAAE;YACxE1B,OAAO,EAAEhG,QAAS;YAClB0G,SAAS,EAAC,yPAAyP;YACnQvF,KAAK,EAAC,eAAe;YACrB+F,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;YAAA;YACzCT,KAAK,EAAE;cACLU,SAAS,EAAE;YACb,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA,eAChB;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlB7H,OAAA;QAAK8G,SAAS,EAAC,kDAAkD;QAAAvB,QAAA,gBAE/DvF,OAAA,CAACH,MAAM,CAAC2F,GAAG;UACTsB,SAAS,EAAC,sDAAsD;UAAAvB,QAAA,gBAEhEvF,OAAA,CAACF,eAAe;YAACiI,IAAI,EAAC,MAAM;YAAAxC,QAAA,eAC1BvF,OAAA,CAACH,MAAM,CAAC2F,GAAG;cAETa,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAE3E,KAAK,EAAE;cAAK,CAAE;cACrC4E,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAE3E,KAAK,EAAE;cAAE,CAAE;cAClCkF,IAAI,EAAE;gBAAEP,OAAO,EAAE,CAAC;gBAAE3E,KAAK,EAAE;cAAK,CAAE;cAClC6E,UAAU,EAAE;gBACVI,QAAQ,EAAE,GAAG;gBACboB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gBACtB1B,OAAO,EAAE;kBAAEM,QAAQ,EAAE;gBAAI;cAC3B,CAAE;cACFE,SAAS,EAAC,gFAAgF;cAAAvB,QAAA,EAEzFnE,WAAW,CAACZ,KAAK,CAAC,CAACc,KAAK,CAAC2G,QAAQ,CAAC,MAAM,CAAC,gBACxCjI,OAAA;gBACEkI,GAAG,EAAE,WAAW9G,WAAW,CAACZ,KAAK,CAAC,CAACc,KAAK,EAAG;gBAC3C6G,QAAQ;gBACRC,KAAK;gBACL1G,IAAI;gBACJ2G,WAAW;gBACXvB,SAAS,EAAC,cAAc;gBACxBC,KAAK,EAAE;kBACLuB,QAAQ,EAAE,wBAAwB;kBAClCtB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdsB,SAAS,EAAE,OAAO;kBAClB3H,QAAQ,EAAE,UAAU;kBACpBmF,GAAG,EAAE,KAAK;kBACVF,IAAI,EAAE,KAAK;kBACX2C,SAAS,EAAE,SAASpH,WAAW,CAACZ,KAAK,CAAC,CAACmB,KAAK,eAAeP,WAAW,CAACZ,KAAK,CAAC,CAACoB,UAAU,KAAKR,WAAW,CAACZ,KAAK,CAAC,CAACqB,UAAU,GAAG;kBAC7H4G,aAAa,EAAE,MAAM;kBACrBC,UAAU,EAAE,MAAM;kBAClBlC,UAAU,EAAE;gBACd,CAAE;gBACFmC,OAAO,EAAGpB,CAAC,IAAK;kBACd;kBACAA,CAAC,CAACqB,MAAM,CAAC7B,KAAK,CAAC8B,OAAO,GAAG,MAAM;gBACjC;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEF7H,OAAA;gBACEkI,GAAG,EAAE,WAAW9G,WAAW,CAACZ,KAAK,CAAC,CAACc,KAAK,EAAG;gBAC3CwH,GAAG,EAAEtI,KAAM;gBACXuI,SAAS,EAAE,KAAM;gBACjBjC,SAAS,EAAC,cAAc;gBACxBC,KAAK,EAAE;kBACLuB,QAAQ,EAAE,wBAAwB;kBAClCtB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdsB,SAAS,EAAE,OAAO;kBAClBS,cAAc,EAAE,MAAM;kBACtBC,MAAM,EAAE,gCAAgC;kBACxCrI,QAAQ,EAAE,UAAU;kBACpBmF,GAAG,EAAE,KAAK;kBACVF,IAAI,EAAE,KAAK;kBACX2C,SAAS,EAAE,SAASpH,WAAW,CAACZ,KAAK,CAAC,CAACmB,KAAK,eAAeP,WAAW,CAACZ,KAAK,CAAC,CAACoB,UAAU,KAAKR,WAAW,CAACZ,KAAK,CAAC,CAACqB,UAAU,GAAG;kBAC7H2E,UAAU,EAAE,sBAAsB;kBAClCiC,aAAa,EAAE,MAAM;kBACrBC,UAAU,EAAE;gBACd,CAAE;gBACFC,OAAO,EAAGpB,CAAC,IAAK;kBACd;kBACAA,CAAC,CAACqB,MAAM,CAAC7B,KAAK,CAAC8B,OAAO,GAAG,MAAM;kBAC/BtB,CAAC,CAACqB,MAAM,CAACM,aAAa,CAACC,SAAS,GAAG;AACvD;AACA,kDAAkDhE,aAAa,CAAC,CAAC,0BAA0BA,aAAa,CAAC,CAAC;AAC1G;AACA,+EAA+EA,aAAa,CAAC,CAAC;AAC9F,+EAA+EA,aAAa,CAAC,CAAC;AAC9F,gEAAgEA,aAAa,CAAC,CAAC;AAC/E,0EAA0EA,aAAa,CAAC,CAAC;AACzF;AACA;AACA,qBAAqB;gBACH;cAAE;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF,GA1EIrH,KAAK;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2EA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGlB7H,OAAA,CAACH,MAAM,CAAC2F,GAAG;YACTsB,SAAS,EAAC,4DAA4D;YACtEC,KAAK,EAAE;cACLqC,WAAW,EAAEjE,aAAa,CAAC,CAAC;cAC5BkE,SAAS,EAAE,wCAAwC;cACnD5B,SAAS,EAAE,YAAYtC,aAAa,CAAC,CAAC;YACxC;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAGb7H,OAAA;UAAK8G,SAAS,EAAC,aAAa;UAAAvB,QAAA,gBAC1BvF,OAAA;YACE8G,SAAS,EAAC,yDAAyD;YACnEC,KAAK,EAAE;cACLtF,KAAK,EAAE0D,aAAa,CAAC;YACvB,CAAE;YAAAI,QAAA,EAEDF,YAAY,CAAC;UAAC;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACN7H,OAAA;YAAK8G,SAAS,EAAC,kDAAkD;YAAAvB,QAAA,EAAC;UAElE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN7H,OAAA;YAAK8G,SAAS,EAAC,uBAAuB;YAAAvB,QAAA,GAAAhF,mBAAA,GACnCa,WAAW,CAACZ,KAAK,CAAC,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoBiB;UAAW;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGI;EAAC,gBACX,CAAC;AAEP,CAAC;AAACvH,EAAA,CAvfIH,eAAe;AAAAmJ,EAAA,GAAfnJ,eAAe;AAyfrB,eAAeA,eAAe;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}