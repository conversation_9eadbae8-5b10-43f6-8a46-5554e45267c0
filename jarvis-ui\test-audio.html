<!DOCTYPE html>
<html>
<head>
    <title>Audio Test for Beat Detection</title>
    <style>
        body {
            background: #000;
            color: #00ff00;
            font-family: monospace;
            padding: 20px;
        }
        button {
            background: #00ff00;
            color: black;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #00cc00;
        }
        .audio-controls {
            margin: 20px 0;
        }
        .frequency-test {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎵 Audio Test for Beat Detection</h1>
    <p>Use this page to generate test audio for your beat detection backends.</p>
    
    <div class="audio-controls">
        <h2>🎯 Test Tones</h2>
        <div class="frequency-test">
            <button onclick="playTone(40, 2000)">Sub Bass (40Hz)</button>
            <button onclick="playTone(100, 2000)">Bass (100Hz)</button>
            <button onclick="playTone(500, 2000)">Mid (500Hz)</button>
            <button onclick="playTone(2000, 2000)">Upper Mid (2kHz)</button>
            <button onclick="playTone(8000, 2000)">Treble (8kHz)</button>
        </div>
        
        <div class="frequency-test">
            <button onclick="playBeats(100, 120)">Bass Beats (120 BPM)</button>
            <button onclick="playBeats(500, 140)">Mid Beats (140 BPM)</button>
            <button onclick="playBeats(2000, 160)">Treble Beats (160 BPM)</button>
        </div>
        
        <div class="frequency-test">
            <button onclick="stopAll()">🛑 Stop All</button>
            <button onclick="playWhiteNoise()">📻 White Noise</button>
            <button onclick="playMusic()">🎵 Test Music</button>
        </div>
    </div>
    
    <div class="audio-controls">
        <h2>🔊 Volume Control</h2>
        <input type="range" id="volumeSlider" min="0" max="100" value="50" onchange="updateVolume()">
        <span id="volumeDisplay">50%</span>
    </div>
    
    <div class="audio-controls">
        <h2>📊 Instructions</h2>
        <ol>
            <li>🎵 <strong>Play test tones</strong> to check frequency detection</li>
            <li>🥁 <strong>Play beats</strong> to test beat detection algorithms</li>
            <li>🔄 <strong>Switch between backends</strong> in your JARVIS app</li>
            <li>📈 <strong>Compare detection accuracy</strong> between C# and Python</li>
            <li>🎯 <strong>Adjust volume</strong> if needed for better detection</li>
        </ol>
    </div>

    <script>
        let audioContext;
        let oscillators = [];
        let gainNodes = [];
        let masterGain;
        
        function initAudio() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                masterGain = audioContext.createGain();
                masterGain.connect(audioContext.destination);
                masterGain.gain.value = 0.5; // 50% volume
            }
        }
        
        function playTone(frequency, duration) {
            initAudio();
            
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(masterGain);
            
            oscillator.frequency.value = frequency;
            oscillator.type = 'sine';
            
            // Envelope
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
            
            oscillator.start();
            oscillator.stop(audioContext.currentTime + duration / 1000);
            
            console.log(`🎵 Playing ${frequency}Hz tone for ${duration}ms`);
        }
        
        function playBeats(frequency, bpm) {
            initAudio();
            stopAll();
            
            const interval = 60000 / bpm; // ms per beat
            let beatCount = 0;
            
            const beatInterval = setInterval(() => {
                playTone(frequency, 200);
                beatCount++;
                
                if (beatCount >= 16) { // Play 16 beats
                    clearInterval(beatInterval);
                }
            }, interval);
            
            console.log(`🥁 Playing ${frequency}Hz beats at ${bpm} BPM`);
        }
        
        function playWhiteNoise() {
            initAudio();
            stopAll();
            
            const bufferSize = audioContext.sampleRate * 2; // 2 seconds
            const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < bufferSize; i++) {
                data[i] = Math.random() * 2 - 1;
            }
            
            const source = audioContext.createBufferSource();
            const gainNode = audioContext.createGain();
            
            source.buffer = buffer;
            source.connect(gainNode);
            gainNode.connect(masterGain);
            gainNode.gain.value = 0.1; // Lower volume for noise
            
            source.start();
            oscillators.push(source);
            gainNodes.push(gainNode);
            
            console.log('📻 Playing white noise');
        }
        
        function playMusic() {
            // Create a simple electronic beat pattern
            initAudio();
            stopAll();
            
            const pattern = [
                { freq: 60, time: 0, duration: 200 },      // Kick
                { freq: 8000, time: 250, duration: 50 },   // Hi-hat
                { freq: 200, time: 500, duration: 150 },   // Snare
                { freq: 8000, time: 750, duration: 50 },   // Hi-hat
                { freq: 60, time: 1000, duration: 200 },   // Kick
                { freq: 8000, time: 1250, duration: 50 },  // Hi-hat
                { freq: 200, time: 1500, duration: 150 },  // Snare
                { freq: 8000, time: 1750, duration: 50 },  // Hi-hat
            ];
            
            pattern.forEach(note => {
                setTimeout(() => {
                    playTone(note.freq, note.duration);
                }, note.time);
            });
            
            // Loop the pattern
            const loopInterval = setInterval(() => {
                pattern.forEach(note => {
                    setTimeout(() => {
                        playTone(note.freq, note.duration);
                    }, note.time);
                });
            }, 2000);
            
            // Stop after 10 seconds
            setTimeout(() => {
                clearInterval(loopInterval);
            }, 10000);
            
            console.log('🎵 Playing test music pattern');
        }
        
        function stopAll() {
            oscillators.forEach(osc => {
                try {
                    osc.stop();
                } catch (e) {
                    // Already stopped
                }
            });
            oscillators = [];
            gainNodes = [];
            console.log('🛑 Stopped all audio');
        }
        
        function updateVolume() {
            const slider = document.getElementById('volumeSlider');
            const display = document.getElementById('volumeDisplay');
            const volume = slider.value / 100;
            
            if (masterGain) {
                masterGain.gain.value = volume;
            }
            
            display.textContent = slider.value + '%';
            console.log(`🔊 Volume set to ${slider.value}%`);
        }
        
        // Initialize on first user interaction
        document.addEventListener('click', initAudio, { once: true });
    </script>
</body>
</html>
