import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ExpandableSidebar from './ui/ExpandableSidebar';
import SystemAudioStatus from './ui/SystemAudioStatus';
import AudioBackendTester from './AudioBackendTester';
import usePythonAudioBackend from '../hooks/usePythonAudioBackend';

const Jarvis = ({ onExitToHome, onEnterChat }) => {
  const [state, setState] = useState('startup');
  const [micActive, setMicActive] = useState(false);
  const [inputText, setInputText] = useState('');
  const [isInputFocused, setIsInputFocused] = useState(false);
  // C# NVIDIA HDMI Audio Backend - Primary audio source
  const {
    audioLevel,
    bassLevel,
    midLevel,
    trebleLevel,
    isAudioActive,
    beatDetected,
    isSystemAudio,
    audioSource
  } = usePythonAudioBackend({
    enabled: true,
    serverUrl: 'ws://localhost:8765', // C# backend runs on same port
    beatThreshold: 0.03, // Maximum sensitivity - very low threshold
    updateInterval: 16
  });

  // Handle mic toggle with state switching
  const handleMicToggle = (checked) => {
    setMicActive(checked);

    if (checked) {
      // Turn on mic -> switch to listening mode
      setState('listening');
    } else {
      // Turn off mic -> return to rest mode (unless processing/speaking)
      if (state === 'listening') {
        setState('rest');
      }
      // If it's thinking/speaking/startup, don't change state
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  // State content configuration
  const stateConfig = {
    startup: {
      media: 'startup.gif',
      title: 'SYSTEM INITIALIZATION',
      description: 'J.A.R.V.I.S. ONLINE',
      color: '#00eeff',
      loop: true
    },
    rest: {
      media: 'rest.mp4',
      title: 'STANDBY MODE',
      description: 'AWAITING COMMAND',
      color: '#00eeff',
      loop: true
    },
    listening: {
      media: 'listening.gif',
      title: 'LISTENING',
      description: 'PROCESSING AUDIO INPUT',
      color: '#ff00aa',
      loop: true
    },
    thinking: {
      media: 'thinking.gif',
      title: 'PROCESSING',
      description: 'ANALYZING REQUEST',
      color: '#ff9900',
      loop: true
    },
    speaking: {
      media: 'speaking.gif',
      title: 'RESPONDING',
      description: 'OUTPUT GENERATION',
      color: '#00ff88',
      loop: true
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black p-8 relative">
      {/* System Audio Status - Top Right */}
      <div className="fixed top-4 right-4 z-20">
        <SystemAudioStatus
          isSystemAudio={isSystemAudio}
          audioSource={audioSource}
          audioLevel={audioLevel}
        />
      </div>

      {/* Expandable Sidebar - Left Side */}
      <div className="fixed left-0 top-1/2 transform -translate-y-1/2 z-10">
        <ExpandableSidebar />
      </div>

      {/* Centered Media Display */}
      <div className="flex flex-col items-center justify-center space-y-8">
        <motion.div
          className="relative w-96 h-96 rounded-full overflow-hidden"
          animate={{
            scale: 1 + (audioLevel * 0.2) + (beatDetected ? 0.15 : 0), // Audio-reactive scaling
            filter: `brightness(${1 + (audioLevel * 0.3)}) contrast(${1 + (bassLevel * 0.2)})`,
          }}
          transition={{
            scale: { type: "spring", damping: 20, stiffness: 300 },
            filter: { duration: 0.1 }
          }}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={state}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.05 }}
              transition={{
                duration: 0.6,
                ease: [0.4, 0, 0.2, 1],
                opacity: { duration: 0.4 }
              }}
              className="absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div"
            >
              {stateConfig[state].media.endsWith('.mp4') ? (
                <video
                  src={`/assets/${stateConfig[state].media}`}
                  autoPlay
                  muted
                  loop
                  playsInline
                  className="w-full h-full object-cover rounded-full"
                  style={{ clipPath: 'circle(50% at 50% 50%)' }}
                />
              ) : (
                <img
                  src={`/assets/${stateConfig[state].media}`}
                  alt={state}
                  className={`w-full h-full object-cover rounded-full ${
                    state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''
                  }`}
                  style={{
                    clipPath: 'circle(50% at 50% 50%)',
                    imageRendering: 'auto',
                    filter: 'contrast(1.1) brightness(1.05)',
                    transition: 'all 0.3s ease-in-out'
                  }}
                />
              )}
            </motion.div>
          </AnimatePresence>

          {/* Pulsing Halo - Audio Reactive */}
          <motion.div
            className="absolute inset-0 rounded-full border-4 pointer-events-none"
            style={{ borderColor: stateConfig[state].color }}
            animate={{
              opacity: [0.3 + (audioLevel * 0.4), 0.8 + (audioLevel * 0.2), 0.3 + (audioLevel * 0.4)],
              scale: [1 + (bassLevel * 0.1), 1.1 + (audioLevel * 0.2) + (beatDetected ? 0.3 : 0), 1 + (bassLevel * 0.1)],
              borderWidth: 4 + (audioLevel * 6),
              boxShadow: `0 0 ${20 + (audioLevel * 40)}px ${stateConfig[state].color}${Math.floor(60 + (audioLevel * 40)).toString(16)}`
            }}
            transition={{
              duration: Math.max(0.5, 2 - (audioLevel * 1.5)), // Faster pulsing with more audio
              repeat: Infinity,
              borderWidth: { duration: 0.1 },
              boxShadow: { duration: 0.1 }
            }}
          />
        </motion.div>

        {/* Status Text */}
        <motion.div
          className="text-center space-y-4"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1
            className="text-4xl font-bold tracking-tighter"
            style={{
              color: stateConfig[state].color
            }}
            animate={{
              scale: 1 + (audioLevel * 0.1) + (beatDetected ? 0.05 : 0),
              textShadow: `0 0 ${20 + (audioLevel * 30)}px currentColor, 0 0 ${40 + (audioLevel * 60)}px currentColor`
            }}
            variants={itemVariants}
            transition={{
              scale: { type: "spring", damping: 25, stiffness: 400 },
              textShadow: { duration: 0.1 }
            }}
          >
            {stateConfig[state].title}
          </motion.h1>

          <motion.p
            className="text-xl text-cyan-200 font-light"
            variants={itemVariants}
            animate={{
              opacity: 0.8 + (midLevel * 0.2),
              scale: 1 + (trebleLevel * 0.05)
            }}
            transition={{
              opacity: { duration: 0.2 },
              scale: { duration: 0.3 }
            }}
          >
            {stateConfig[state].description}
          </motion.p>
        </motion.div>

        {/* Mic Button - Center Bottom */}
        <motion.div
          className="mt-12"
          variants={itemVariants}
          initial="hidden"
          animate="visible"
        >
          <label className="container">
            <input
              type="checkbox"
              checked={micActive}
              onChange={(e) => handleMicToggle(e.target.checked)}
            />
            <div className="checkmark">
              {/* Mic Off Icon - with vertical cross line */}
              <svg className="icon No" viewBox="0 0 24 24" fill="#dc6b6b" style={{ color: '#dc6b6b' }}>
                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                {/* Tilted cross line towards right */}
                <line
                  x1="6" y1="4"
                  x2="18" y2="20"
                  stroke="#dc6b6b"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                />
              </svg>
              <span className="name No">Mic Off</span>

              {/* Mic On Icon - active listening */}
              <svg className="icon Yes" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                {/* Sound waves indicator */}
                <circle cx="12" cy="8" r="1" opacity="0.6">
                  <animate attributeName="r" values="1;2;1" dur="1s" repeatCount="indefinite"/>
                  <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1s" repeatCount="indefinite"/>
                </circle>
                <circle cx="12" cy="8" r="3" opacity="0.3">
                  <animate attributeName="r" values="3;4;3" dur="1.5s" repeatCount="indefinite"/>
                  <animate attributeName="opacity" values="0.3;0.1;0.3" dur="1.5s" repeatCount="indefinite"/>
                </circle>
              </svg>
              <span className="name Yes">Listening</span>
            </div>
          </label>
        </motion.div>

        {/* Text Input - Below Mic Button */}
        <motion.div
          className="flex justify-center"
          variants={itemVariants}
          initial="hidden"
          animate="visible"
        >
          <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>
            <input
              type="text"
              className="input"
              placeholder="Type something intelligent (Press Enter to chat)"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onFocus={() => setIsInputFocused(true)}
              onBlur={() => setIsInputFocused(false)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && inputText.trim()) {
                  // Handle text input submission - open chat screen
                  const message = inputText.trim();
                  setInputText('');
                  onEnterChat(message);
                }
              }}
            />
            <div className="input-border"></div>
          </div>
        </motion.div>
      </div>

      {/* State Control Menu - Bottom Left */}
      <div className="absolute bottom-6 left-6">
        <motion.div
          className="menu"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {Object.keys(stateConfig).map((s) => (
            <motion.button
              key={s}
              className={`link ${state === s ? 'active' : ''}`}
              style={{
                backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',
                border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'
              }}
              whileHover={{
                scale: 1.05,
                boxShadow: `0 0 12px ${stateConfig[s].color}50`
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setState(s)}
              variants={itemVariants}
              title={s.toUpperCase()}
            >
              <div className="link-icon">
                <div
                  className="w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold"
                  style={{
                    backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',
                    color: 'white',
                    fontSize: '10px'
                  }}
                >
                  {s.charAt(0).toUpperCase()}
                </div>
              </div>
              <span className="link-title">
                {s.charAt(0).toUpperCase() + s.slice(1)}
              </span>
            </motion.button>
          ))}

          {/* Home Button */}
          {onExitToHome && (
            <motion.button
              className="link"
              style={{
                backgroundColor: 'transparent',
                border: '1px solid rgba(255, 255, 255, 0.3)'
              }}
              whileHover={{
                scale: 1.05,
                boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={onExitToHome}
              variants={itemVariants}
              title="HOME"
            >
              <div className="link-icon">
                <div
                  className="w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.6)',
                    color: 'black',
                    fontSize: '10px'
                  }}
                >
                  H
                </div>
              </div>
              <span className="link-title">Home</span>
            </motion.button>
          )}
        </motion.div>
      </div>

      {/* Audio Backend Tester for comparing C# vs Python backends */}
      <AudioBackendTester />

    </div>
  );
};

export default Jarvis;