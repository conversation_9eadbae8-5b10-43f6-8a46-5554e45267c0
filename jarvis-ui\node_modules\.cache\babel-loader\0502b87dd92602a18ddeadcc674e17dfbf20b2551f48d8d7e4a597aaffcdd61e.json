{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useRef, useCallback } from 'react';\nconst usePythonAudioBackend = ({\n  enabled = true,\n  serverUrl = 'ws://localhost:8765',\n  reconnectInterval = 3000,\n  maxReconnectAttempts = 10\n}) => {\n  _s();\n  // Audio data state\n  const [audioLevel, setAudioLevel] = useState(0);\n  const [bassLevel, setBassLevel] = useState(0);\n  const [midLevel, setMidLevel] = useState(0);\n  const [trebleLevel, setTrebleLevel] = useState(0);\n  const [beatDetected, setBeatDetected] = useState(false);\n  const [audioSource, setAudioSource] = useState('none');\n  const [isSystemAudio, setIsSystemAudio] = useState(false);\n\n  // Connection state\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [reconnectAttempts, setReconnectAttempts] = useState(0);\n\n  // Refs\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const lastDataRef = useRef(null);\n\n  // Beat detection smoothing\n  const beatTimeoutRef = useRef(null);\n  const connect = useCallback(() => {\n    var _wsRef$current;\n    if (!enabled) {\n      console.log('🚫 Python audio backend disabled');\n      return;\n    }\n    if (((_wsRef$current = wsRef.current) === null || _wsRef$current === void 0 ? void 0 : _wsRef$current.readyState) === WebSocket.OPEN) {\n      console.log('🔌 WebSocket already connected');\n      return;\n    }\n    try {\n      console.log(`🔌 Connecting to Python audio backend: ${serverUrl}`);\n      const ws = new WebSocket(serverUrl);\n      wsRef.current = ws;\n      ws.onopen = () => {\n        console.log('✅ Connected to Python audio backend');\n        setIsConnected(true);\n        setConnectionError(null);\n        setReconnectAttempts(0);\n      };\n      ws.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n\n          // Update audio data\n          setAudioLevel(data.audioLevel || 0);\n          setBassLevel(data.bassLevel || 0);\n          setMidLevel(data.midLevel || 0);\n          setTrebleLevel(data.trebleLevel || 0);\n          setAudioSource(data.audioSource || 'none');\n          setIsSystemAudio(data.isSystemAudio || false);\n\n          // Handle beat detection with visual feedback\n          if (data.beatDetected) {\n            setBeatDetected(true);\n\n            // Clear previous timeout\n            if (beatTimeoutRef.current) {\n              clearTimeout(beatTimeoutRef.current);\n            }\n\n            // Reset beat detection after 200ms\n            beatTimeoutRef.current = setTimeout(() => {\n              setBeatDetected(false);\n            }, 200);\n          }\n          lastDataRef.current = data;\n        } catch (error) {\n          console.error('❌ Error parsing audio data:', error);\n        }\n      };\n      ws.onclose = event => {\n        console.log('🔌 WebSocket connection closed:', event.code, event.reason);\n        setIsConnected(false);\n        wsRef.current = null;\n\n        // Attempt to reconnect if enabled and not manually closed\n        if (enabled && event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {\n          console.log(`🔄 Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`);\n          setReconnectAttempts(prev => prev + 1);\n          reconnectTimeoutRef.current = setTimeout(() => {\n            connect();\n          }, reconnectInterval);\n        } else if (reconnectAttempts >= maxReconnectAttempts) {\n          setConnectionError('Max reconnection attempts reached');\n          console.error('❌ Max reconnection attempts reached');\n        }\n      };\n      ws.onerror = error => {\n        console.error('❌ WebSocket error:', error);\n        setConnectionError('Connection failed');\n      };\n    } catch (error) {\n      console.error('❌ Failed to create WebSocket connection:', error);\n      setConnectionError(error.message);\n    }\n  }, [enabled, serverUrl, reconnectInterval, maxReconnectAttempts, reconnectAttempts]);\n  const disconnect = useCallback(() => {\n    console.log('🔌 Disconnecting from Python audio backend');\n\n    // Clear reconnection timeout\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n\n    // Clear beat timeout\n    if (beatTimeoutRef.current) {\n      clearTimeout(beatTimeoutRef.current);\n      beatTimeoutRef.current = null;\n    }\n\n    // Close WebSocket connection\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect');\n      wsRef.current = null;\n    }\n\n    // Reset state\n    setIsConnected(false);\n    setConnectionError(null);\n    setReconnectAttempts(0);\n    setAudioLevel(0);\n    setBassLevel(0);\n    setMidLevel(0);\n    setTrebleLevel(0);\n    setBeatDetected(false);\n    setAudioSource('none');\n    setIsSystemAudio(false);\n  }, []);\n  const reconnect = useCallback(() => {\n    console.log('🔄 Manual reconnection requested');\n    disconnect();\n    setTimeout(() => {\n      setReconnectAttempts(0);\n      connect();\n    }, 1000);\n  }, [disconnect, connect]);\n\n  // Auto-connect when enabled\n  useEffect(() => {\n    if (enabled) {\n      connect();\n    } else {\n      disconnect();\n    }\n    return () => {\n      disconnect();\n    };\n  }, [enabled, connect, disconnect]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  // Connection status helper\n  const getConnectionStatus = useCallback(() => {\n    if (isConnected) {\n      return {\n        status: 'connected',\n        message: `Connected to Python backend (${isSystemAudio ? 'System Audio' : 'Microphone'})`\n      };\n    } else if (connectionError) {\n      return {\n        status: 'error',\n        message: `Connection error: ${connectionError}`\n      };\n    } else if (reconnectAttempts > 0) {\n      return {\n        status: 'reconnecting',\n        message: `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`\n      };\n    } else {\n      return {\n        status: 'disconnected',\n        message: 'Not connected to Python backend'\n      };\n    }\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, isSystemAudio]);\n\n  // Debug info\n  const getDebugInfo = useCallback(() => {\n    var _wsRef$current2;\n    return {\n      isConnected,\n      connectionError,\n      reconnectAttempts,\n      maxReconnectAttempts,\n      serverUrl,\n      lastData: lastDataRef.current,\n      wsReadyState: (_wsRef$current2 = wsRef.current) === null || _wsRef$current2 === void 0 ? void 0 : _wsRef$current2.readyState\n    };\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, serverUrl]);\n\n  // Computed values\n  const isAudioActive = audioLevel > 5; // Consider audio active if level > 5%\n\n  return {\n    // Audio data\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    beatDetected,\n    isAudioActive,\n    audioSource,\n    isSystemAudio,\n    // Connection state\n    isConnected,\n    connectionError,\n    reconnectAttempts,\n    // Connection controls\n    connect,\n    disconnect,\n    reconnect,\n    // Helpers\n    getConnectionStatus,\n    getDebugInfo\n  };\n};\n_s(usePythonAudioBackend, \"cJEydY06ouJ/GpkemzQJqBI+OVQ=\");\nexport default usePythonAudioBackend;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useCallback", "usePythonAudioBackend", "enabled", "serverUrl", "reconnectInterval", "maxReconnectAttempts", "_s", "audioLevel", "setAudioLevel", "bassLevel", "setBassLevel", "midLevel", "setMidLevel", "trebleLevel", "setTrebleLevel", "beatDetected", "setBeatDetected", "audioSource", "setAudioSource", "isSystemAudio", "setIsSystemAudio", "isConnected", "setIsConnected", "connectionError", "setConnectionError", "reconnectAttempts", "setReconnectAttempts", "wsRef", "reconnectTimeoutRef", "lastDataRef", "beatTimeoutRef", "connect", "_wsRef$current", "console", "log", "current", "readyState", "WebSocket", "OPEN", "ws", "onopen", "onmessage", "event", "data", "JSON", "parse", "clearTimeout", "setTimeout", "error", "onclose", "code", "reason", "prev", "onerror", "message", "disconnect", "close", "reconnect", "getConnectionStatus", "status", "getDebugInfo", "_wsRef$current2", "lastData", "wsReadyState", "isAudioActive"], "sources": ["C:/Users/<USER>/My Code Work/UI Designolddd/jarvis-ui/src/hooks/usePythonAudioBackend.js"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react';\n\nconst usePythonAudioBackend = ({ \n  enabled = true, \n  serverUrl = 'ws://localhost:8765',\n  reconnectInterval = 3000,\n  maxReconnectAttempts = 10\n}) => {\n  // Audio data state\n  const [audioLevel, setAudioLevel] = useState(0);\n  const [bassLevel, setBassLevel] = useState(0);\n  const [midLevel, setMidLevel] = useState(0);\n  const [trebleLevel, setTrebleLevel] = useState(0);\n  const [beatDetected, setBeatDetected] = useState(false);\n  const [audioSource, setAudioSource] = useState('none');\n  const [isSystemAudio, setIsSystemAudio] = useState(false);\n  \n  // Connection state\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [reconnectAttempts, setReconnectAttempts] = useState(0);\n  \n  // Refs\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const lastDataRef = useRef(null);\n  \n  // Beat detection smoothing\n  const beatTimeoutRef = useRef(null);\n  \n  const connect = useCallback(() => {\n    if (!enabled) {\n      console.log('🚫 Python audio backend disabled');\n      return;\n    }\n    \n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      console.log('🔌 WebSocket already connected');\n      return;\n    }\n    \n    try {\n      console.log(`🔌 Connecting to Python audio backend: ${serverUrl}`);\n      \n      const ws = new WebSocket(serverUrl);\n      wsRef.current = ws;\n      \n      ws.onopen = () => {\n        console.log('✅ Connected to Python audio backend');\n        setIsConnected(true);\n        setConnectionError(null);\n        setReconnectAttempts(0);\n      };\n      \n      ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          \n          // Update audio data\n          setAudioLevel(data.audioLevel || 0);\n          setBassLevel(data.bassLevel || 0);\n          setMidLevel(data.midLevel || 0);\n          setTrebleLevel(data.trebleLevel || 0);\n          setAudioSource(data.audioSource || 'none');\n          setIsSystemAudio(data.isSystemAudio || false);\n          \n          // Handle beat detection with visual feedback\n          if (data.beatDetected) {\n            setBeatDetected(true);\n            \n            // Clear previous timeout\n            if (beatTimeoutRef.current) {\n              clearTimeout(beatTimeoutRef.current);\n            }\n            \n            // Reset beat detection after 200ms\n            beatTimeoutRef.current = setTimeout(() => {\n              setBeatDetected(false);\n            }, 200);\n          }\n          \n          lastDataRef.current = data;\n          \n        } catch (error) {\n          console.error('❌ Error parsing audio data:', error);\n        }\n      };\n      \n      ws.onclose = (event) => {\n        console.log('🔌 WebSocket connection closed:', event.code, event.reason);\n        setIsConnected(false);\n        wsRef.current = null;\n        \n        // Attempt to reconnect if enabled and not manually closed\n        if (enabled && event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {\n          console.log(`🔄 Attempting to reconnect (${reconnectAttempts + 1}/${maxReconnectAttempts})...`);\n          setReconnectAttempts(prev => prev + 1);\n          \n          reconnectTimeoutRef.current = setTimeout(() => {\n            connect();\n          }, reconnectInterval);\n        } else if (reconnectAttempts >= maxReconnectAttempts) {\n          setConnectionError('Max reconnection attempts reached');\n          console.error('❌ Max reconnection attempts reached');\n        }\n      };\n      \n      ws.onerror = (error) => {\n        console.error('❌ WebSocket error:', error);\n        setConnectionError('Connection failed');\n      };\n      \n    } catch (error) {\n      console.error('❌ Failed to create WebSocket connection:', error);\n      setConnectionError(error.message);\n    }\n  }, [enabled, serverUrl, reconnectInterval, maxReconnectAttempts, reconnectAttempts]);\n  \n  const disconnect = useCallback(() => {\n    console.log('🔌 Disconnecting from Python audio backend');\n    \n    // Clear reconnection timeout\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n    \n    // Clear beat timeout\n    if (beatTimeoutRef.current) {\n      clearTimeout(beatTimeoutRef.current);\n      beatTimeoutRef.current = null;\n    }\n    \n    // Close WebSocket connection\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect');\n      wsRef.current = null;\n    }\n    \n    // Reset state\n    setIsConnected(false);\n    setConnectionError(null);\n    setReconnectAttempts(0);\n    setAudioLevel(0);\n    setBassLevel(0);\n    setMidLevel(0);\n    setTrebleLevel(0);\n    setBeatDetected(false);\n    setAudioSource('none');\n    setIsSystemAudio(false);\n  }, []);\n  \n  const reconnect = useCallback(() => {\n    console.log('🔄 Manual reconnection requested');\n    disconnect();\n    setTimeout(() => {\n      setReconnectAttempts(0);\n      connect();\n    }, 1000);\n  }, [disconnect, connect]);\n  \n  // Auto-connect when enabled\n  useEffect(() => {\n    if (enabled) {\n      connect();\n    } else {\n      disconnect();\n    }\n    \n    return () => {\n      disconnect();\n    };\n  }, [enabled, connect, disconnect]);\n  \n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n  \n  // Connection status helper\n  const getConnectionStatus = useCallback(() => {\n    if (isConnected) {\n      return {\n        status: 'connected',\n        message: `Connected to Python backend (${isSystemAudio ? 'System Audio' : 'Microphone'})`\n      };\n    } else if (connectionError) {\n      return {\n        status: 'error',\n        message: `Connection error: ${connectionError}`\n      };\n    } else if (reconnectAttempts > 0) {\n      return {\n        status: 'reconnecting',\n        message: `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`\n      };\n    } else {\n      return {\n        status: 'disconnected',\n        message: 'Not connected to Python backend'\n      };\n    }\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, isSystemAudio]);\n  \n  // Debug info\n  const getDebugInfo = useCallback(() => {\n    return {\n      isConnected,\n      connectionError,\n      reconnectAttempts,\n      maxReconnectAttempts,\n      serverUrl,\n      lastData: lastDataRef.current,\n      wsReadyState: wsRef.current?.readyState\n    };\n  }, [isConnected, connectionError, reconnectAttempts, maxReconnectAttempts, serverUrl]);\n  \n  // Computed values\n  const isAudioActive = audioLevel > 5; // Consider audio active if level > 5%\n\n  return {\n    // Audio data\n    audioLevel,\n    bassLevel,\n    midLevel,\n    trebleLevel,\n    beatDetected,\n    isAudioActive,\n    audioSource,\n    isSystemAudio,\n\n    // Connection state\n    isConnected,\n    connectionError,\n    reconnectAttempts,\n\n    // Connection controls\n    connect,\n    disconnect,\n    reconnect,\n\n    // Helpers\n    getConnectionStatus,\n    getDebugInfo\n  };\n};\n\nexport default usePythonAudioBackend;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAEhE,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,OAAO,GAAG,IAAI;EACdC,SAAS,GAAG,qBAAqB;EACjCC,iBAAiB,GAAG,IAAI;EACxBC,oBAAoB,GAAG;AACzB,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;;EAE7D;EACA,MAAM8B,KAAK,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAM6B,mBAAmB,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM8B,WAAW,GAAG9B,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAM+B,cAAc,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMgC,OAAO,GAAG/B,WAAW,CAAC,MAAM;IAAA,IAAAgC,cAAA;IAChC,IAAI,CAAC9B,OAAO,EAAE;MACZ+B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C;IACF;IAEA,IAAI,EAAAF,cAAA,GAAAL,KAAK,CAACQ,OAAO,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;MAChDL,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C;IACF;IAEA,IAAI;MACFD,OAAO,CAACC,GAAG,CAAC,0CAA0C/B,SAAS,EAAE,CAAC;MAElE,MAAMoC,EAAE,GAAG,IAAIF,SAAS,CAAClC,SAAS,CAAC;MACnCwB,KAAK,CAACQ,OAAO,GAAGI,EAAE;MAElBA,EAAE,CAACC,MAAM,GAAG,MAAM;QAChBP,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDZ,cAAc,CAAC,IAAI,CAAC;QACpBE,kBAAkB,CAAC,IAAI,CAAC;QACxBE,oBAAoB,CAAC,CAAC,CAAC;MACzB,CAAC;MAEDa,EAAE,CAACE,SAAS,GAAIC,KAAK,IAAK;QACxB,IAAI;UACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;;UAEnC;UACAnC,aAAa,CAACmC,IAAI,CAACpC,UAAU,IAAI,CAAC,CAAC;UACnCG,YAAY,CAACiC,IAAI,CAAClC,SAAS,IAAI,CAAC,CAAC;UACjCG,WAAW,CAAC+B,IAAI,CAAChC,QAAQ,IAAI,CAAC,CAAC;UAC/BG,cAAc,CAAC6B,IAAI,CAAC9B,WAAW,IAAI,CAAC,CAAC;UACrCK,cAAc,CAACyB,IAAI,CAAC1B,WAAW,IAAI,MAAM,CAAC;UAC1CG,gBAAgB,CAACuB,IAAI,CAACxB,aAAa,IAAI,KAAK,CAAC;;UAE7C;UACA,IAAIwB,IAAI,CAAC5B,YAAY,EAAE;YACrBC,eAAe,CAAC,IAAI,CAAC;;YAErB;YACA,IAAIc,cAAc,CAACK,OAAO,EAAE;cAC1BW,YAAY,CAAChB,cAAc,CAACK,OAAO,CAAC;YACtC;;YAEA;YACAL,cAAc,CAACK,OAAO,GAAGY,UAAU,CAAC,MAAM;cACxC/B,eAAe,CAAC,KAAK,CAAC;YACxB,CAAC,EAAE,GAAG,CAAC;UACT;UAEAa,WAAW,CAACM,OAAO,GAAGQ,IAAI;QAE5B,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdf,OAAO,CAACe,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF,CAAC;MAEDT,EAAE,CAACU,OAAO,GAAIP,KAAK,IAAK;QACtBT,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEQ,KAAK,CAACQ,IAAI,EAAER,KAAK,CAACS,MAAM,CAAC;QACxE7B,cAAc,CAAC,KAAK,CAAC;QACrBK,KAAK,CAACQ,OAAO,GAAG,IAAI;;QAEpB;QACA,IAAIjC,OAAO,IAAIwC,KAAK,CAACQ,IAAI,KAAK,IAAI,IAAIzB,iBAAiB,GAAGpB,oBAAoB,EAAE;UAC9E4B,OAAO,CAACC,GAAG,CAAC,+BAA+BT,iBAAiB,GAAG,CAAC,IAAIpB,oBAAoB,MAAM,CAAC;UAC/FqB,oBAAoB,CAAC0B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;UAEtCxB,mBAAmB,CAACO,OAAO,GAAGY,UAAU,CAAC,MAAM;YAC7ChB,OAAO,CAAC,CAAC;UACX,CAAC,EAAE3B,iBAAiB,CAAC;QACvB,CAAC,MAAM,IAAIqB,iBAAiB,IAAIpB,oBAAoB,EAAE;UACpDmB,kBAAkB,CAAC,mCAAmC,CAAC;UACvDS,OAAO,CAACe,KAAK,CAAC,qCAAqC,CAAC;QACtD;MACF,CAAC;MAEDT,EAAE,CAACc,OAAO,GAAIL,KAAK,IAAK;QACtBf,OAAO,CAACe,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1CxB,kBAAkB,CAAC,mBAAmB,CAAC;MACzC,CAAC;IAEH,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChExB,kBAAkB,CAACwB,KAAK,CAACM,OAAO,CAAC;IACnC;EACF,CAAC,EAAE,CAACpD,OAAO,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEoB,iBAAiB,CAAC,CAAC;EAEpF,MAAM8B,UAAU,GAAGvD,WAAW,CAAC,MAAM;IACnCiC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;IAEzD;IACA,IAAIN,mBAAmB,CAACO,OAAO,EAAE;MAC/BW,YAAY,CAAClB,mBAAmB,CAACO,OAAO,CAAC;MACzCP,mBAAmB,CAACO,OAAO,GAAG,IAAI;IACpC;;IAEA;IACA,IAAIL,cAAc,CAACK,OAAO,EAAE;MAC1BW,YAAY,CAAChB,cAAc,CAACK,OAAO,CAAC;MACpCL,cAAc,CAACK,OAAO,GAAG,IAAI;IAC/B;;IAEA;IACA,IAAIR,KAAK,CAACQ,OAAO,EAAE;MACjBR,KAAK,CAACQ,OAAO,CAACqB,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MAC9C7B,KAAK,CAACQ,OAAO,GAAG,IAAI;IACtB;;IAEA;IACAb,cAAc,CAAC,KAAK,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,CAAC,CAAC;IACvBlB,aAAa,CAAC,CAAC,CAAC;IAChBE,YAAY,CAAC,CAAC,CAAC;IACfE,WAAW,CAAC,CAAC,CAAC;IACdE,cAAc,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtBE,cAAc,CAAC,MAAM,CAAC;IACtBE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMqC,SAAS,GAAGzD,WAAW,CAAC,MAAM;IAClCiC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CqB,UAAU,CAAC,CAAC;IACZR,UAAU,CAAC,MAAM;MACfrB,oBAAoB,CAAC,CAAC,CAAC;MACvBK,OAAO,CAAC,CAAC;IACX,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACwB,UAAU,EAAExB,OAAO,CAAC,CAAC;;EAEzB;EACAjC,SAAS,CAAC,MAAM;IACd,IAAII,OAAO,EAAE;MACX6B,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACLwB,UAAU,CAAC,CAAC;IACd;IAEA,OAAO,MAAM;MACXA,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACrD,OAAO,EAAE6B,OAAO,EAAEwB,UAAU,CAAC,CAAC;;EAElC;EACAzD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXyD,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMG,mBAAmB,GAAG1D,WAAW,CAAC,MAAM;IAC5C,IAAIqB,WAAW,EAAE;MACf,OAAO;QACLsC,MAAM,EAAE,WAAW;QACnBL,OAAO,EAAE,gCAAgCnC,aAAa,GAAG,cAAc,GAAG,YAAY;MACxF,CAAC;IACH,CAAC,MAAM,IAAII,eAAe,EAAE;MAC1B,OAAO;QACLoC,MAAM,EAAE,OAAO;QACfL,OAAO,EAAE,qBAAqB/B,eAAe;MAC/C,CAAC;IACH,CAAC,MAAM,IAAIE,iBAAiB,GAAG,CAAC,EAAE;MAChC,OAAO;QACLkC,MAAM,EAAE,cAAc;QACtBL,OAAO,EAAE,oBAAoB7B,iBAAiB,IAAIpB,oBAAoB;MACxE,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLsD,MAAM,EAAE,cAAc;QACtBL,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC,EAAE,CAACjC,WAAW,EAAEE,eAAe,EAAEE,iBAAiB,EAAEpB,oBAAoB,EAAEc,aAAa,CAAC,CAAC;;EAE1F;EACA,MAAMyC,YAAY,GAAG5D,WAAW,CAAC,MAAM;IAAA,IAAA6D,eAAA;IACrC,OAAO;MACLxC,WAAW;MACXE,eAAe;MACfE,iBAAiB;MACjBpB,oBAAoB;MACpBF,SAAS;MACT2D,QAAQ,EAAEjC,WAAW,CAACM,OAAO;MAC7B4B,YAAY,GAAAF,eAAA,GAAElC,KAAK,CAACQ,OAAO,cAAA0B,eAAA,uBAAbA,eAAA,CAAezB;IAC/B,CAAC;EACH,CAAC,EAAE,CAACf,WAAW,EAAEE,eAAe,EAAEE,iBAAiB,EAAEpB,oBAAoB,EAAEF,SAAS,CAAC,CAAC;;EAEtF;EACA,MAAM6D,aAAa,GAAGzD,UAAU,GAAG,CAAC,CAAC,CAAC;;EAEtC,OAAO;IACL;IACAA,UAAU;IACVE,SAAS;IACTE,QAAQ;IACRE,WAAW;IACXE,YAAY;IACZiD,aAAa;IACb/C,WAAW;IACXE,aAAa;IAEb;IACAE,WAAW;IACXE,eAAe;IACfE,iBAAiB;IAEjB;IACAM,OAAO;IACPwB,UAAU;IACVE,SAAS;IAET;IACAC,mBAAmB;IACnBE;EACF,CAAC;AACH,CAAC;AAACtD,EAAA,CArPIL,qBAAqB;AAuP3B,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}