using System;
using System.Threading.Tasks;
using System.Net.WebSockets;
using System.Net;
using System.Text;
using System.Text.Json;
using NAudio.Wave;
using NAudio.CoreAudioApi;
using System.Threading;

namespace NvidiaHdmiAudio
{
    public class AudioData
    {
        public double audioLevel { get; set; }
        public double bassLevel { get; set; }
        public double midLevel { get; set; }
        public double trebleLevel { get; set; }
        public bool beatDetected { get; set; }
        public string source { get; set; } = "";
        public string status { get; set; } = "";
    }

    public class NvidiaHdmiAudioCapture
    {
        private WasapiLoopbackCapture? capture;
        private HttpListener? httpListener;
        private AudioData audioData = new AudioData();
        private bool isRunning = false;
        private readonly object audioLock = new object();

        public async Task StartAsync()
        {
            Console.WriteLine("🎵 C# NVIDIA HDMI Audio Solution");
            Console.WriteLine("==================================================");
            Console.WriteLine("🚀 Using NAudio WASAPI - Best Windows Audio API!");
            Console.WriteLine("🎯 Works perfectly with NVIDIA HDMI!");
            Console.WriteLine();

            try
            {
                // Initialize WASAPI Loopback Capture
                InitializeAudioCapture();
                
                // Start WebSocket server
                await StartWebSocketServer();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        private void InitializeAudioCapture()
        {
            try
            {
                // Get default audio endpoint (whatever is currently playing)
                var enumerator = new MMDeviceEnumerator();
                var defaultDevice = enumerator.GetDefaultAudioEndpoint(DataFlow.Render, Role.Console);
                
                Console.WriteLine($"✅ Audio Device: {defaultDevice.FriendlyName}");
                
                // Check if it's NVIDIA HDMI
                if (defaultDevice.FriendlyName.ToLower().Contains("nvidia") && 
                    defaultDevice.FriendlyName.ToLower().Contains("hdmi"))
                {
                    Console.WriteLine("🎯 NVIDIA HDMI detected! Perfect compatibility!");
                }

                audioData.source = $"WASAPI: {defaultDevice.FriendlyName}";

                // Initialize WASAPI Loopback Capture
                capture = new WasapiLoopbackCapture(defaultDevice);
                capture.DataAvailable += OnDataAvailable;
                capture.RecordingStopped += OnRecordingStopped;

                // Start capturing
                capture.StartRecording();
                isRunning = true;

                Console.WriteLine("✅ WASAPI Loopback Capture started!");
                Console.WriteLine("🎵 Now capturing system audio in real-time!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Audio capture initialization failed: {ex.Message}");
                audioData.status = "ERROR";
            }
        }

        private void OnDataAvailable(object? sender, WaveInEventArgs e)
        {
            try
            {
                // Convert byte array to float array
                var buffer = new float[e.BytesRecorded / 4];
                Buffer.BlockCopy(e.Buffer, 0, buffer, 0, e.BytesRecorded);

                // Calculate audio levels
                double sum = 0;
                double maxSample = 0;

                for (int i = 0; i < buffer.Length; i += 2) // Stereo, so step by 2
                {
                    // Convert stereo to mono
                    float sample = (buffer[i] + buffer[i + 1]) / 2.0f;
                    double absSample = Math.Abs(sample);

                    sum += absSample * absSample;
                    maxSample = Math.Max(maxSample, absSample);
                }

                // Calculate RMS (Root Mean Square) for overall level - Increased sensitivity
                double rms = Math.Sqrt(sum / (buffer.Length / 2));
                double audioLevel = Math.Min(rms * 200, 100.0); // 2x amplification for better detection

                // Ensure no NaN or Infinity values
                if (double.IsNaN(audioLevel) || double.IsInfinity(audioLevel))
                    audioLevel = 0.0;

                // Simple frequency band simulation (in real app, use FFT)
                double bassLevel = Math.Min(audioLevel * (0.8 + new Random().NextDouble() * 0.4), 100.0);
                double midLevel = Math.Min(audioLevel * (0.9 + new Random().NextDouble() * 0.2), 100.0);
                double trebleLevel = Math.Min(audioLevel * (0.7 + new Random().NextDouble() * 0.6), 100.0);

                // Ensure no NaN or Infinity values for frequency bands
                if (double.IsNaN(bassLevel) || double.IsInfinity(bassLevel))
                    bassLevel = 0.0;
                if (double.IsNaN(midLevel) || double.IsInfinity(midLevel))
                    midLevel = 0.0;
                if (double.IsNaN(trebleLevel) || double.IsInfinity(trebleLevel))
                    trebleLevel = 0.0;

                // Simple beat detection - Increased sensitivity
                bool beatDetected = audioLevel > 8 && maxSample > 0.15;

                // Update audio data thread-safely
                lock (audioLock)
                {
                    audioData.audioLevel = audioLevel;
                    audioData.bassLevel = bassLevel;
                    audioData.midLevel = midLevel;
                    audioData.trebleLevel = trebleLevel;
                    audioData.beatDetected = beatDetected;
                    audioData.status = "CAPTURING";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Audio processing error: {ex.Message}");
            }
        }

        private void OnRecordingStopped(object? sender, StoppedEventArgs e)
        {
            Console.WriteLine("🛑 Audio capture stopped");
            if (e.Exception != null)
            {
                Console.WriteLine($"❌ Recording error: {e.Exception.Message}");
            }
        }

        private async Task StartWebSocketServer()
        {
            httpListener = new HttpListener();
            httpListener.Prefixes.Add("http://localhost:8765/");
            httpListener.Start();

            Console.WriteLine("✅ C# WebSocket server running on ws://localhost:8765");
            Console.WriteLine("🎯 Ready for audio-reactive JARVIS!");

            while (isRunning)
            {
                try
                {
                    var context = await httpListener.GetContextAsync();
                    if (context.Request.IsWebSocketRequest)
                    {
                        _ = Task.Run(() => HandleWebSocketAsync(context));
                    }
                    else
                    {
                        context.Response.StatusCode = 400;
                        context.Response.Close();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ WebSocket server error: {ex.Message}");
                }
            }
        }

        private async Task HandleWebSocketAsync(HttpListenerContext context)
        {
            WebSocket? webSocket = null;
            try
            {
                var webSocketContext = await context.AcceptWebSocketAsync(null);
                webSocket = webSocketContext.WebSocket;

                Console.WriteLine("🔌 New WebSocket connection established");

                // Send initial connection message
                var connectionMessage = new
                {
                    type = "connection",
                    status = "connected",
                    message = "Connected to C# NVIDIA HDMI Audio Backend",
                    source = audioData.source,
                    method = "NAudio WASAPI"
                };

                await SendWebSocketMessage(webSocket, connectionMessage);

                // Send audio data continuously
                while (webSocket.State == WebSocketState.Open && isRunning)
                {
                    AudioData currentData;
                    lock (audioLock)
                    {
                        currentData = new AudioData
                        {
                            audioLevel = audioData.audioLevel,
                            bassLevel = audioData.bassLevel,
                            midLevel = audioData.midLevel,
                            trebleLevel = audioData.trebleLevel,
                            beatDetected = audioData.beatDetected,
                            source = audioData.source,
                            status = audioData.status
                        };
                    }

                    var message = new
                    {
                        type = "audioData",
                        audioLevel = currentData.audioLevel,
                        bassLevel = currentData.bassLevel,
                        midLevel = currentData.midLevel,
                        trebleLevel = currentData.trebleLevel,
                        beatDetected = currentData.beatDetected,
                        source = currentData.source,
                        status = currentData.status
                    };

                    await SendWebSocketMessage(webSocket, message);
                    await Task.Delay(50); // 20 FPS
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ WebSocket connection error: {ex.Message}");
            }
            finally
            {
                webSocket?.Dispose();
                Console.WriteLine("🔌 WebSocket connection closed");
            }
        }

        private async Task SendWebSocketMessage(WebSocket webSocket, object message)
        {
            try
            {
                var json = JsonSerializer.Serialize(message);
                var buffer = Encoding.UTF8.GetBytes(json);
                await webSocket.SendAsync(
                    new ArraySegment<byte>(buffer),
                    WebSocketMessageType.Text,
                    true,
                    CancellationToken.None
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to send WebSocket message: {ex.Message}");
            }
        }

        public void Stop()
        {
            isRunning = false;
            capture?.StopRecording();
            capture?.Dispose();
            httpListener?.Stop();
            httpListener?.Close();
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            var audioCapture = new NvidiaHdmiAudioCapture();
            
            Console.CancelKeyPress += (sender, e) =>
            {
                e.Cancel = true;
                Console.WriteLine("\n🛑 Shutting down...");
                audioCapture.Stop();
                Environment.Exit(0);
            };

            await audioCapture.StartAsync();
        }
    }
}
