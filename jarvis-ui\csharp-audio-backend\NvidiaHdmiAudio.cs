using System;
using System.Threading.Tasks;
using System.Net.WebSockets;
using System.Net;
using System.Text;
using System.Text.Json;
using NAudio.Wave;
using NAudio.CoreAudioApi;
using System.Threading;
using System.Numerics;
using System.Linq;

namespace NvidiaHdmiAudio
{
    public class AudioData
    {
        public double audioLevel { get; set; }
        public double bassLevel { get; set; }
        public double midLevel { get; set; }
        public double trebleLevel { get; set; }
        public bool beatDetected { get; set; }
        public string source { get; set; } = "";
        public string status { get; set; } = "";

        // Advanced frequency band data
        public double subBassLevel { get; set; }
        public double lowMidrangeLevel { get; set; }
        public double upperMidrangeLevel { get; set; }
        public double presenceLevel { get; set; }
        public double brillianceLevel { get; set; }

        // Beat detection for each frequency band
        public bool subBassBeat { get; set; }
        public bool bassBeat { get; set; }
        public bool lowMidrangeBeat { get; set; }
        public bool midrangeBeat { get; set; }
        public bool upperMidrangeBeat { get; set; }
        public bool presenceBeat { get; set; }
        public bool brillianceBeat { get; set; }
    }

    public class AdvancedBeatDetector
    {
        private double subBassMax = 0.01;
        private double bassMax = 0.01;
        private double lowMidrangeMax = 0.01;
        private double midrangeMax = 0.01;
        private double upperMidrangeMax = 0.01;
        private double presenceMax = 0.01;
        private double brillianceMax = 0.01;

        private bool subBassBeat = false;
        private bool bassBeat = false;
        private bool lowMidrangeBeat = false;
        private bool midrangeBeat = false;
        private bool upperMidrangeBeat = false;
        private bool presenceBeat = false;
        private bool brillianceBeat = false;

        private readonly int sampleRate;

        public AdvancedBeatDetector(int sampleRate)
        {
            this.sampleRate = sampleRate;
        }

        public AudioData DetectBeats(float[] audioBuffer)
        {
            // Perform FFT
            var fftData = PerformFFT(audioBuffer);
            var freqs = GenerateFrequencies(fftData.Length);

            // Extract frequency bands
            var subBass = GetMaxInFrequencyRange(fftData, freqs, 20, 60);
            var bass = GetMaxInFrequencyRange(fftData, freqs, 60, 250);
            var lowMidrange = GetMaxInFrequencyRange(fftData, freqs, 250, 500);
            var midrange = GetMaxInFrequencyRange(fftData, freqs, 500, 2000);
            var upperMidrange = GetMaxInFrequencyRange(fftData, freqs, 2000, 4000);
            var presence = GetMaxInFrequencyRange(fftData, freqs, 4000, 6000);
            var brilliance = GetMaxInFrequencyRange(fftData, freqs, 6000, 20000);

            // Update maximums
            subBassMax = Math.Max(subBassMax, subBass);
            bassMax = Math.Max(bassMax, bass);
            lowMidrangeMax = Math.Max(lowMidrangeMax, lowMidrange);
            midrangeMax = Math.Max(midrangeMax, midrange);
            upperMidrangeMax = Math.Max(upperMidrangeMax, upperMidrange);
            presenceMax = Math.Max(presenceMax, presence);
            brillianceMax = Math.Max(brillianceMax, brilliance);

            // Detect beats with hysteresis
            subBassBeat = DetectBeatInBand(subBass, subBassMax, subBassBeat);
            bassBeat = DetectBeatInBand(bass, bassMax, bassBeat);
            lowMidrangeBeat = DetectBeatInBand(lowMidrange, lowMidrangeMax, lowMidrangeBeat);
            midrangeBeat = DetectBeatInBand(midrange, midrangeMax, midrangeBeat);
            upperMidrangeBeat = DetectBeatInBand(upperMidrange, upperMidrangeMax, upperMidrangeBeat);
            presenceBeat = DetectBeatInBand(presence, presenceMax, presenceBeat);
            brillianceBeat = DetectBeatInBand(brilliance, brillianceMax, brillianceBeat);

            // Calculate overall levels
            double audioLevel = Math.Min((bass + midrange + upperMidrange) * 100, 100.0);
            bool overallBeat = bassBeat || midrangeBeat || upperMidrangeBeat;

            return new AudioData
            {
                audioLevel = audioLevel,
                bassLevel = Math.Min(bass * 100, 100.0),
                midLevel = Math.Min(midrange * 100, 100.0),
                trebleLevel = Math.Min((upperMidrange + presence + brilliance) * 100, 100.0),
                beatDetected = overallBeat,

                subBassLevel = Math.Min(subBass * 100, 100.0),
                lowMidrangeLevel = Math.Min(lowMidrange * 100, 100.0),
                upperMidrangeLevel = Math.Min(upperMidrange * 100, 100.0),
                presenceLevel = Math.Min(presence * 100, 100.0),
                brillianceLevel = Math.Min(brilliance * 100, 100.0),

                subBassBeat = subBassBeat,
                bassBeat = bassBeat,
                lowMidrangeBeat = lowMidrangeBeat,
                midrangeBeat = midrangeBeat,
                upperMidrangeBeat = upperMidrangeBeat,
                presenceBeat = presenceBeat,
                brillianceBeat = brillianceBeat,

                source = "NVIDIA HDMI Audio",
                status = "CAPTURING"
            };
        }

        private bool DetectBeatInBand(double current, double max, bool currentBeat)
        {
            if (current >= max * 0.85 && !currentBeat)
            {
                return true; // Beat detected
            }
            else if (current < max * 0.25)
            {
                return false; // Reset beat state
            }
            return currentBeat; // Maintain current state
        }

        private double[] PerformFFT(float[] audioBuffer)
        {
            int n = audioBuffer.Length;
            var complex = new Complex[n];

            // Convert to complex numbers
            for (int i = 0; i < n; i++)
            {
                complex[i] = new Complex(audioBuffer[i], 0);
            }

            // Simple FFT implementation (for production, use a proper FFT library)
            var fft = SimpleFFT(complex);

            // Return magnitude spectrum (first half)
            var result = new double[n / 2];
            for (int i = 0; i < n / 2; i++)
            {
                result[i] = fft[i].Magnitude / n;
            }

            return result;
        }

        private Complex[] SimpleFFT(Complex[] x)
        {
            int n = x.Length;
            if (n <= 1) return x;

            // Divide
            var even = new Complex[n / 2];
            var odd = new Complex[n / 2];
            for (int i = 0; i < n / 2; i++)
            {
                even[i] = x[2 * i];
                odd[i] = x[2 * i + 1];
            }

            // Conquer
            var evenFFT = SimpleFFT(even);
            var oddFFT = SimpleFFT(odd);

            // Combine
            var result = new Complex[n];
            for (int k = 0; k < n / 2; k++)
            {
                var t = Complex.FromPolarCoordinates(1.0, -2.0 * Math.PI * k / n) * oddFFT[k];
                result[k] = evenFFT[k] + t;
                result[k + n / 2] = evenFFT[k] - t;
            }

            return result;
        }

        private double[] GenerateFrequencies(int fftLength)
        {
            var freqs = new double[fftLength];
            for (int i = 0; i < fftLength; i++)
            {
                freqs[i] = (double)i * sampleRate / (2.0 * fftLength);
            }
            return freqs;
        }

        private double GetMaxInFrequencyRange(double[] fftData, double[] freqs, double minFreq, double maxFreq)
        {
            double max = 0.0;
            for (int i = 0; i < freqs.Length && i < fftData.Length; i++)
            {
                if (freqs[i] >= minFreq && freqs[i] <= maxFreq)
                {
                    max = Math.Max(max, fftData[i]);
                }
            }
            return max;
        }
    }

    public class NvidiaHdmiAudioCapture
    {
        private WasapiLoopbackCapture? capture;
        private HttpListener? httpListener;
        private AudioData audioData = new AudioData();
        private bool isRunning = false;
        private readonly object audioLock = new object();
        private AdvancedBeatDetector? beatDetector;

        public async Task StartAsync()
        {
            Console.WriteLine("🎵 C# NVIDIA HDMI Audio Solution");
            Console.WriteLine("==================================================");
            Console.WriteLine("🚀 Using NAudio WASAPI - Best Windows Audio API!");
            Console.WriteLine("🎯 Works perfectly with NVIDIA HDMI!");
            Console.WriteLine();

            try
            {
                // Initialize WASAPI Loopback Capture
                InitializeAudioCapture();
                
                // Start WebSocket server
                await StartWebSocketServer();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        private void InitializeAudioCapture()
        {
            try
            {
                // Get default audio endpoint (whatever is currently playing)
                var enumerator = new MMDeviceEnumerator();
                var defaultDevice = enumerator.GetDefaultAudioEndpoint(DataFlow.Render, Role.Console);
                
                Console.WriteLine($"✅ Audio Device: {defaultDevice.FriendlyName}");
                
                // Check if it's NVIDIA HDMI
                if (defaultDevice.FriendlyName.ToLower().Contains("nvidia") && 
                    defaultDevice.FriendlyName.ToLower().Contains("hdmi"))
                {
                    Console.WriteLine("🎯 NVIDIA HDMI detected! Perfect compatibility!");
                }

                audioData.source = $"WASAPI: {defaultDevice.FriendlyName}";

                // Initialize WASAPI Loopback Capture
                capture = new WasapiLoopbackCapture(defaultDevice);
                capture.DataAvailable += OnDataAvailable;
                capture.RecordingStopped += OnRecordingStopped;

                // Initialize advanced beat detector with sample rate
                beatDetector = new AdvancedBeatDetector(capture.WaveFormat.SampleRate);
                Console.WriteLine($"🎵 Advanced Beat Detector initialized (Sample Rate: {capture.WaveFormat.SampleRate} Hz)");

                // Start capturing
                capture.StartRecording();
                isRunning = true;

                Console.WriteLine("✅ WASAPI Loopback Capture started!");
                Console.WriteLine("🎵 Now capturing system audio in real-time!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Audio capture initialization failed: {ex.Message}");
                audioData.status = "ERROR";
            }
        }

        private void OnDataAvailable(object? sender, WaveInEventArgs e)
        {
            try
            {
                if (beatDetector == null) return;

                // Convert byte array to float array
                var buffer = new float[e.BytesRecorded / 4];
                Buffer.BlockCopy(e.Buffer, 0, buffer, 0, e.BytesRecorded);

                // Use advanced beat detector for sophisticated analysis
                var detectedData = beatDetector.DetectBeats(buffer);

                // Update audio data thread-safely
                lock (audioLock)
                {
                    audioData = detectedData;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Audio processing error: {ex.Message}");
            }
        }

        private void OnRecordingStopped(object? sender, StoppedEventArgs e)
        {
            Console.WriteLine("🛑 Audio capture stopped");
            if (e.Exception != null)
            {
                Console.WriteLine($"❌ Recording error: {e.Exception.Message}");
            }
        }

        private async Task StartWebSocketServer()
        {
            httpListener = new HttpListener();
            httpListener.Prefixes.Add("http://localhost:8765/");
            httpListener.Start();

            Console.WriteLine("✅ C# WebSocket server running on ws://localhost:8765");
            Console.WriteLine("🎯 Ready for audio-reactive JARVIS!");

            while (isRunning)
            {
                try
                {
                    var context = await httpListener.GetContextAsync();
                    if (context.Request.IsWebSocketRequest)
                    {
                        _ = Task.Run(() => HandleWebSocketAsync(context));
                    }
                    else
                    {
                        context.Response.StatusCode = 400;
                        context.Response.Close();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ WebSocket server error: {ex.Message}");
                }
            }
        }

        private async Task HandleWebSocketAsync(HttpListenerContext context)
        {
            WebSocket? webSocket = null;
            try
            {
                var webSocketContext = await context.AcceptWebSocketAsync(null);
                webSocket = webSocketContext.WebSocket;

                Console.WriteLine("🔌 New WebSocket connection established");

                // Send initial connection message
                var connectionMessage = new
                {
                    type = "connection",
                    status = "connected",
                    message = "Connected to C# NVIDIA HDMI Audio Backend",
                    source = audioData.source,
                    method = "NAudio WASAPI"
                };

                await SendWebSocketMessage(webSocket, connectionMessage);

                // Send audio data continuously
                while (webSocket.State == WebSocketState.Open && isRunning)
                {
                    AudioData currentData;
                    lock (audioLock)
                    {
                        currentData = new AudioData
                        {
                            audioLevel = audioData.audioLevel,
                            bassLevel = audioData.bassLevel,
                            midLevel = audioData.midLevel,
                            trebleLevel = audioData.trebleLevel,
                            beatDetected = audioData.beatDetected,
                            source = audioData.source,
                            status = audioData.status,

                            // Advanced frequency bands
                            subBassLevel = audioData.subBassLevel,
                            lowMidrangeLevel = audioData.lowMidrangeLevel,
                            upperMidrangeLevel = audioData.upperMidrangeLevel,
                            presenceLevel = audioData.presenceLevel,
                            brillianceLevel = audioData.brillianceLevel,

                            // Advanced beat detection
                            subBassBeat = audioData.subBassBeat,
                            bassBeat = audioData.bassBeat,
                            lowMidrangeBeat = audioData.lowMidrangeBeat,
                            midrangeBeat = audioData.midrangeBeat,
                            upperMidrangeBeat = audioData.upperMidrangeBeat,
                            presenceBeat = audioData.presenceBeat,
                            brillianceBeat = audioData.brillianceBeat
                        };
                    }

                    var message = new
                    {
                        type = "audioData",
                        audioLevel = currentData.audioLevel,
                        bassLevel = currentData.bassLevel,
                        midLevel = currentData.midLevel,
                        trebleLevel = currentData.trebleLevel,
                        beatDetected = currentData.beatDetected,
                        source = currentData.source,
                        status = currentData.status,

                        // Advanced frequency bands for sophisticated visualization
                        subBassLevel = currentData.subBassLevel,
                        lowMidrangeLevel = currentData.lowMidrangeLevel,
                        upperMidrangeLevel = currentData.upperMidrangeLevel,
                        presenceLevel = currentData.presenceLevel,
                        brillianceLevel = currentData.brillianceLevel,

                        // Individual frequency band beat detection
                        subBassBeat = currentData.subBassBeat,
                        bassBeat = currentData.bassBeat,
                        lowMidrangeBeat = currentData.lowMidrangeBeat,
                        midrangeBeat = currentData.midrangeBeat,
                        upperMidrangeBeat = currentData.upperMidrangeBeat,
                        presenceBeat = currentData.presenceBeat,
                        brillianceBeat = currentData.brillianceBeat
                    };

                    await SendWebSocketMessage(webSocket, message);
                    await Task.Delay(50); // 20 FPS
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ WebSocket connection error: {ex.Message}");
            }
            finally
            {
                webSocket?.Dispose();
                Console.WriteLine("🔌 WebSocket connection closed");
            }
        }

        private async Task SendWebSocketMessage(WebSocket webSocket, object message)
        {
            try
            {
                var json = JsonSerializer.Serialize(message);
                var buffer = Encoding.UTF8.GetBytes(json);
                await webSocket.SendAsync(
                    new ArraySegment<byte>(buffer),
                    WebSocketMessageType.Text,
                    true,
                    CancellationToken.None
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to send WebSocket message: {ex.Message}");
            }
        }

        public void Stop()
        {
            isRunning = false;
            capture?.StopRecording();
            capture?.Dispose();
            httpListener?.Stop();
            httpListener?.Close();
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            var audioCapture = new NvidiaHdmiAudioCapture();
            
            Console.CancelKeyPress += (sender, e) =>
            {
                e.Cancel = true;
                Console.WriteLine("\n🛑 Shutting down...");
                audioCapture.Stop();
                Environment.Exit(0);
            };

            await audioCapture.StartAsync();
        }
    }
}
