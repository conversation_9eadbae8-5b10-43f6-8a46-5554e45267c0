# 🎵 Python Advanced Beat Detector

This is a direct implementation of the **maxemitchell/beat-detection-python** algorithm for real-time audio analysis and beat detection.

## 🚀 Features

- **Real FFT Analysis**: Proper Fast Fourier Transform using NumPy
- **7 Frequency Bands**: Sub Bass, Bass, Low Mid, Mid, Upper Mid, Presence, Brilliance
- **Advanced Beat Detection**: Individual beat detection per frequency band
- **Hysteresis Algorithm**: Prevents false beat triggers (90% trigger, 30% reset)
- **WebSocket Streaming**: Real-time data to React frontend
- **System Audio Capture**: Uses PyAudio for microphone/system audio

## 📦 Installation

### Option 1: Automatic Setup (Windows)
```bash
# Run the batch file to install dependencies and start
python-beat-detector/run_beat_detector.bat
```

### Option 2: Manual Setup
```bash
cd python-beat-detector

# Install Python dependencies
pip install -r requirements.txt

# Run the beat detector
python beat_detector.py
```

## 🔗 Connection Details

- **WebSocket Server**: `ws://localhost:8766`
- **Audio Source**: System microphone (default input device)
- **Sample Rate**: 44,100 Hz
- **Chunk Size**: 1024 samples
- **Update Rate**: Real-time (as fast as audio chunks arrive)

## 🎯 Testing Both Backends

### 1. Start Both Backends

**C# Backend (Port 8765):**
```bash
cd csharp-audio-backend
dotnet run
```

**Python Backend (Port 8766):**
```bash
cd python-beat-detector
python beat_detector.py
```

### 2. Use the Audio Backend Tester

Add the `AudioBackendTester` component to your React app:

```javascript
import AudioBackendTester from './components/AudioBackendTester';

function App() {
  return (
    <div>
      {/* Your existing JARVIS UI */}
      <AudioBackendTester />
    </div>
  );
}
```

### 3. Compare Performance

The tester allows you to:
- **Switch between backends** with one click
- **Compare beat detection accuracy** in real-time
- **View all frequency band data** from both systems
- **Monitor connection status** and errors

## 🔍 Expected Differences

### C# WASAPI Backend
- ✅ **System Audio**: Captures actual system output (music, videos, etc.)
- ✅ **NVIDIA HDMI Compatible**: Works with NVIDIA HDMI audio
- ✅ **No Microphone Required**: Direct system audio capture
- ⚠️ **Simulated FFT**: Uses custom FFT implementation

### Python FFT Backend  
- ✅ **True FFT Analysis**: Uses NumPy's optimized FFT
- ✅ **Original Algorithm**: Direct port of maxemitchell's code
- ✅ **Professional Analysis**: Same algorithm used in audio software
- ⚠️ **Microphone Input**: Requires microphone to hear system audio

## 🎵 Frequency Band Breakdown

| Band | Range | Typical Instruments |
|------|-------|-------------------|
| **Sub Bass** | 20-60 Hz | Deep bass, kick drums |
| **Bass** | 60-250 Hz | Bass guitar, low vocals |
| **Low Midrange** | 250-500 Hz | Guitar, piano low notes |
| **Midrange** | 500-2000 Hz | Vocals, most instruments |
| **Upper Midrange** | 2000-4000 Hz | Vocals clarity, cymbals |
| **Presence** | 4000-6000 Hz | Vocal presence, snare |
| **Brilliance** | 6000-20000 Hz | Cymbals, high harmonics |

## 🐛 Troubleshooting

### Python Backend Issues

**"No module named 'pyaudio'":**
```bash
# Windows
pip install pyaudio

# If that fails, try:
pip install pipwin
pipwin install pyaudio
```

**"No audio devices found":**
- Check that your microphone is working
- Try running as administrator
- Check Windows audio settings

**"WebSocket connection failed":**
- Make sure port 8766 is not blocked by firewall
- Check if another application is using port 8766

### C# Backend Issues

**"Audio device not found":**
- Make sure your audio device is set as default in Windows
- Try restarting the application

**"WebSocket connection failed":**
- Make sure port 8765 is not blocked by firewall
- Check if another application is using port 8765

## 🎯 Which Backend Should You Use?

### Use **C# Backend** if:
- You want to capture actual system audio (music, videos)
- You have NVIDIA HDMI audio
- You don't want to use a microphone
- You need reliable system-level audio capture

### Use **Python Backend** if:
- You want the most accurate FFT analysis
- You want to test the original maxemitchell algorithm
- You're okay with microphone input
- You want professional-grade frequency analysis

## 🔄 Next Steps

1. **Test both backends** with the same music
2. **Compare beat detection accuracy** 
3. **Choose the best performing backend** for your use case
4. **Integrate the winner** into your main JARVIS application

The goal is to find which implementation gives you the most responsive and accurate beat detection for your specific audio setup! 🎵🚀
